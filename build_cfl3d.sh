#!/bin/bash
#SBATCH --job-name=cfl3d_build       # 作业名称
#SBATCH --nodes=1                # 使用1个节点
#SBATCH --gres=gpu:1        # 每个节点分配4个GPU
#SBATCH --partition=gpu          # 指定gpu分区

# 打印作业信息
echo "=========================================="
echo "SLURM Job ID: $SLURM_JOB_ID"
echo "SLURM Node List: $SLURM_JOB_NODELIST"
echo "Running on host: $(hostname)"
echo "Initial working directory: $(pwd)"
echo "=========================================="

# 加载必要的模块
echo "Loading modules..."
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

echo "当前加载的模块:"
module list

echo "=========================================="

# 进入CFL3D-SR的build目录 (修复版源代码位置)
cd /home/<USER>/gpuuser255/lmc/CFL3D-SR/build || {
    echo "Error: Cannot change to CFL3D-SR build directory"
    exit 1
}

echo "Current working directory: $(pwd)"
echo "=========================================="

# 验证源文件修复
echo "验证内存修复是否已应用..."
echo "检查 cfl3d.F 文件..."
grep -n "ifree = 0" ../source/cfl3d/dist/cfl3d.F
if [ $? -eq 0 ]; then
    echo "✅ cfl3d.F 内存修复已确认 (ifree = 0)"
else
    echo "❌ 警告: cfl3d.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "检查 ronnie/sizer.F 文件..."
grep -n "ifree = 0" ../source/ronnie/sizer.F
if [ $? -eq 0 ]; then
    echo "✅ ronnie/sizer.F 内存修复已确认 (ifree = 0)"
else
    echo "❌ 警告: ronnie/sizer.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "检查 cfl3d/dist/sizer.F 文件..."
grep -n "ifree = 0" ../source/cfl3d/dist/sizer.F
if [ $? -eq 0 ]; then
    echo "✅ cfl3d/dist/sizer.F 内存修复已确认 (ifree = 0)"
else
    echo "❌ 警告: cfl3d/dist/sizer.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "检查 tools/initialize_field.F 文件..."
grep -n "ifree = 0" ../source/tools/initialize_field.F
if [ $? -eq 0 ]; then
    echo "✅ tools/initialize_field.F 内存修复已确认 (ifree = 0)"
else
    echo "❌ 警告: tools/initialize_field.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "检查 ronnie/ronnie.F 文件..."
grep -n "ifree = 0" ../source/ronnie/ronnie.F
if [ $? -eq 0 ]; then
    echo "✅ ronnie/ronnie.F 内存修复已确认 (ifree = 0)"
else
    echo "❌ 警告: ronnie/ronnie.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "检查 cfl3d/dist/main.F 文件..."
grep -n "deallocate(blocks_clcd)" ../source/cfl3d/dist/main.F
if [ $? -ne 0 ]; then
    echo "✅ cfl3d/dist/main.F 内存修复已确认 (deallocate已注释)"
else
    echo "❌ 警告: cfl3d/dist/main.F 内存修复未找到，可能仍会出现内存错误"
fi

echo "=========================================="

# 清理之前的编译文件
echo "Cleaning previous build files..."
make scruball -f makefile_intel

# 创建软链接
echo "Creating symbolic links..."
make linkall -f makefile_intel

# 编译公共库
echo "Building CFL3D libraries..."
make cfl3d_libs -f makefile_intel -j 8

# 编译MPI版本
echo "Building CFL3D MPI version..."
make cfl3d_mpi -f makefile_intel -j 8

echo "=========================================="
echo "Build completed!"
echo "Checking if executable was created..."

if [ -f "cfl/mpi/cfl3d_mpi" ]; then
    echo "✅ SUCCESS: cfl3d_mpi executable created successfully!"
    echo "Location: $(pwd)/cfl/mpi/cfl3d_mpi"
    ls -la cfl/mpi/cfl3d_mpi
else
    echo "❌ ERROR: cfl3d_mpi executable not found!"
    echo "Check the build log for errors."
    exit 1
fi

echo "=========================================="
echo "Build job completed at $(date)"
