Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21288
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21288
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 159.37 seconds.
  Return Code: 255
  STDOUT:
           0  of           2  is alive
           1  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 56985 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 56986 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x00000000015f97e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2b0eb86d3299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc5abaf]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x690f5e]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8bd9d5]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2b0eb8674555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f35000 r-xp 00000000 00:29 495502208                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01134000-01136000 r--p 00b34000 00:29 495502208                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01136000-0118e000 rw-p 00b36000 00:29 495502208                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0118e000-014a2000 rw-p 00000000 00:00 0 
01534000-01be1000 rw-p 00000000 00:00 0                                  [heap]
2b0eb5f1b000-2b0eb5f3d000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b0eb5f3d000-2b0eb5f3f000 r-xp 00000000 00:00 0                          [vdso]
2b0eb5f3f000-2b0eb5f49000 rw-p 00000000 00:00 0 
2b0eb5f49000-2b0eb5f4a000 rw-s 00000000 00:2d 2595835341                 /dev/shm/Intel_MPI_Kgn7Mi (deleted)
2b0eb5f4a000-2b0eb5f4b000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f4b000-2b0eb5f4c000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f4c000-2b0eb5f4d000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f4d000-2b0eb5f4e000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f4e000-2b0eb5f50000 rw-p 00000000 00:00 0 
2b0eb5f50000-2b0eb5f54000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b0eb5f54000-2b0eb5f66000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b0eb5f66000-2b0eb5f69000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b0eb5f69000-2b0eb5f6a000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b0eb5f6a000-2b0eb5f6b000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b0eb5f6b000-2b0eb5f77000 rw-p 00000000 00:00 0 
2b0eb5f77000-2b0eb5f78000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f78000-2b0eb5f79000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f79000-2b0eb5f7a000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7a000-2b0eb5f7b000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7b000-2b0eb5f7c000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7c000-2b0eb5f7d000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7d000-2b0eb5f7e000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7e000-2b0eb5f7f000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f7f000-2b0eb5f80000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f80000-2b0eb5f81000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f81000-2b0eb5f82000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f82000-2b0eb5f83000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f83000-2b0eb5f84000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f84000-2b0eb5f85000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f85000-2b0eb5f86000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f86000-2b0eb5f87000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f87000-2b0eb5f89000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b0eb5f89000-2b0eb5f8d000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b0eb5f8d000-2b0eb5f8e000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b0eb5f8e000-2b0eb5f8f000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b0eb5f8f000-2b0eb5f90000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b0eb5f90000-2b0eb5f91000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f91000-2b0eb5f92000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f92000-2b0eb5f93000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f93000-2b0eb5f94000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f94000-2b0eb5f95000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f95000-2b0eb5f96000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f96000-2b0eb5f97000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f97000-2b0eb5f98000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f98000-2b0eb5f99000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f99000-2b0eb5f9a000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f9a000-2b0eb5f9b000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f9b000-2b0eb5f9c000 rw-p 00000000 00:00 0 
2b0eb5f9c000-2b0eb5f9d000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2b0eb5f9d000-2b0eb5fa0000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b0eb5fa0000-2b0eb5faf000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b0eb5faf000-2b0eb5fb6000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b0eb5fb6000-2b0eb5fb7000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b0eb5fb7000-2b0eb5fb8000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b0eb5fb8000-2b0eb6130000 rw-p 00000000 00:00 0 
2b0eb613c000-2b0eb613d000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b0eb613d000-2b0eb613e000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b0eb613e000-2b0eb613f000 rw-p 00000000 00:00 0 
2b0eb613f000-2b0eb62c7000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b0eb62c7000-2b0eb64c7000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b0eb64c7000-2b0eb64cb000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b0eb64cb000-2b0eb64cf000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b0eb64cf000-2b0eb64f3000 rw-p 00000000 00:00 0 
2b0eb64f3000-2b0eb737d000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b0eb737d000-2b0eb757d000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b0eb757d000-2b0eb758e000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b0eb758e000-2b0eb759f000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b0eb759f000-2b0eb7d28000 rw-p 00000000 00:00 0 
2b0eb7d28000-2b0eb7d2a000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b0eb7d2a000-2b0eb7f2a000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b0eb7f2a000-2b0eb7f2b000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b0eb7f2b000-2b0eb7f2c000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b0eb7f2c000-2b0eb7f33000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b0eb7f33000-2b0eb8132000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b0eb8132000-2b0eb8133000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b0eb8133000-2b0eb8134000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b0eb8134000-2b0eb814b000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b0eb814b000-2b0eb834a000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b0eb834a000-2b0eb834b000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b0eb834b000-2b0eb834c000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b0eb834c000-2b0eb8350000 rw-p 00000000 00:00 0 
2b0eb8350000-2b0eb8451000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b0eb8451000-2b0eb8650000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b0eb8650000-2b0eb8651000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b0eb8651000-2b0eb8652000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b0eb8652000-2b0eb8747000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8747000-2b0eb8748000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8748000-2b0eb874a000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb874a000-2b0eb874b000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb874b000-2b0eb8751000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8751000-2b0eb8753000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8753000-2b0eb8815000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8815000-2b0eb8a15000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8a15000-2b0eb8a19000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8a19000-2b0eb8a1b000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b0eb8a1b000-2b0eb8a20000 rw-p 00000000 00:00 0 
2b0eb8a20000-2b0eb8a2a000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b0eb8a2a000-2b0eb8c2a000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b0eb8c2a000-2b0eb8c2b000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b0eb8c2b000-2b0eb8c2c000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b0eb8c2c000-2b0f5c3fe000 rw-s 00000000 00:2d 2595835342                 /dev/shm/Intel_MPI_HLzKYZ (deleted)
2b0f5c3fe000-2b0f5c446000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b0f5c446000-2b0f5c645000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b0f5c645000-2b0f5c649000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b0f5c649000-2b0f5c6a0000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b0f5c6a0000-2b0f5c89f000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b0f5c89f000-2b0f5c8a3000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b0f5c8a3000-2b0f5c8ba000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b0f5c8ba000-2b0f5cab9000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b0f5cab9000-2b0f5caba000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b0f5caba000-2b0f5cabb000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b0f5cabb000-2b0f5cabc000 rw-p 00000000 00:00 0 
2b0f5cabc000-2b0f5cad6000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b0f5cad6000-2b0f5ccd5000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b0f5ccd5000-2b0f5ccd6000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b0f5ccd6000-2b0f5ccd7000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b0f5ccd7000-2b0f5ccf5000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b0f5ccf5000-2b0f5cef5000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b0f5cef5000-2b0f5cef7000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b0f5cef7000-2b0f5cef8000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b0f5cef8000-2b0f5cf5c000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b0f5cf5c000-2b0f5d15b000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b0f5d15b000-2b0f5d15e000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b0f5d15e000-2b0f5d163000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b0f5d163000-2b0f5d165000 rw-p 00000000 00:00 0 
2b0f5d165000-2b0f5d1ad000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b0f5d1ad000-2b0f5d3ad000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b0f5d3ad000-2b0f5d3ae000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b0f5d3ae000-2b0f5d3af000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b0f5d3af000-2b0f5d3b1000 rw-p 00000000 00:00 0 
2b0f5d3b1000-2b0f5d3b5000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b0f5d3b5000-2b0f5d5b4000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b0f5d5b4000-2b0f5d5b5000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b0f5d5b5000-2b0f5d5b6000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b0f5d5b6000-2b0f5d5c1000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b0f5d5c1000-2b0f5d7c0000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b0f5d7c0000-2b0f5d7c1000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b0f5d7c1000-2b0f5d7c2000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b0f5d7c2000-2b0f5d806000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b0f5d806000-2b0f5da06000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b0f5da06000-2b0f5da09000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b0f5da09000-2b0f5da64000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b0f5da64000-2b0f5dc63000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b0f5dc63000-2b0f5dc67000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b0f5dc67000-2b0f5dcb6000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b0f5dcb6000-2b0f5deb6000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b0f5deb6000-2b0f5deba000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b0f5deba000-2b0f5df0d000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b0f5df0d000-2b0f5e10d000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b0f5e10d000-2b0f5e111000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b0f5e111000-2b0f5e244000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b0f5e244000-2b0f5e443000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b0f5e443000-2b0f5e44b000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b0f5e44b000-2b0f5e471000 rw-p 00000000 00:00 0 
2b0f5e471000-2b0f5e4b2000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b0f5e4b2000-2b0f5e6b2000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b0f5e6b2000-2b0f5e6b5000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b0f5e6b5000-2b0f5e70e000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b0f5e70e000-2b0f5e90d000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b0f5e90d000-2b0f5e90e000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b0f5e90e000-2b0f5e911000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b0f5e911000-2b0f5e937000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b0f5e937000-2b0f5eb37000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b0f5eb37000-2b0f5eb38000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b0f5eb38000-2b0f5eb3c000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b0f5eb3c000-2b0f5ec7b000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b0f5ec7b000-2b0f5ee7b000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b0f5ee7b000-2b0f5ee8e000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b0f5ee8e000-2b0f5ee95000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b0f5ee95000-2b0f5ee9d000 rw-p 00000000 00:00 0 
2b0f5ee9d000-2b0f5eeaf000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b0f5eeaf000-2b0f5f0ae000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b0f5f0ae000-2b0f5f0af000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b0f5f0af000-2b0f5f0b0000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b0f5f0b0000-2b0f5f0b1000 rw-p 00000000 00:00 0 
2b0f5f0b1000-2b0f5f10c000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b0f5f10c000-2b0f5f30c000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b0f5f30c000-2b0f5f30d000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b0f5f30d000-2b0f5f312000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b0f5f312000-2b0f5f31d000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b0f5f31d000-2b0f5f51c000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b0f5f51c000-2b0f5f51d000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b0f5f51d000-2b0f5f51e000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b0f5f51e000-2b0f5f521000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b0f5f521000-2b0f5f721000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b0f5f721000-2b0f5f722000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b0f5f722000-2b0f5f723000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b0f5f723000-2b0f5f727000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b0f5f727000-2b0f5f926000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b0f5f926000-2b0f5f927000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b0f5f927000-2b0f5f928000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b0f5f928000-2b0f5f9ad000 rw-p 00000000 00:00 0 
2b0f5f9ad000-2b0f5f9ae000 ---p 00000000 00:00 0 
2b0f5f9ae000-2b0f5fe00000 rw-p 00000000 00:00 0 
2b0f5fe00000-2b0f60400000 rw-p 00000000 00:00 0 
2b0f60400000-2b0f60600000 rw-p 00000000 00:00 0 
2b0f60600000-2b0f62c00000 rw-p 00000000 00:00 0 
2b0f62c00000-2b0f62e00000 rw-p 00000000 00:00 0 
2b0f62e00000-2b0f64200000 rw-p 00000000 00:00 0 
2b0f64200000-2b0f6a600000 rw-p 00000000 00:00 0 
2b0f6a600000-2b0f6b000000 rw-p 00000000 00:00 0 
2b0f6b000000-2b0f6b152000 rw-p 00000000 00:00 0 
2b0f6c000000-2b0f6c021000 rw-p 00000000 00:00 0 
2b0f6c021000-2b0f70000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003200000 rw-p 00000000 00:00 0 
7ffcd3525000-7ffcd354d000 rw-p 00000000 00:00 0                          [stack]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
