#!/bin/bash
# 路径验证脚本

echo "=========================================="
echo "CFL3D 路径验证脚本"
echo "=========================================="

# 定义路径
PROJECT_ROOT="/home/<USER>/gpuuser255/lmc/Agent-R1-q3"
CFL3D_ROOT="/home/<USER>/gpuuser255/lmc/CFL3D-SR"
CFL3D_EXECUTABLE="${CFL3D_ROOT}/build/cfl/mpi/cfl3d_mpi"
TEST_DIR="${PROJECT_ROOT}/cfl3d_test"
PYTHON_SCRIPT="${PROJECT_ROOT}/run_cfl3d_in_test_dir.py"

echo "检查路径配置:"
echo "项目根目录: ${PROJECT_ROOT}"
echo "CFL3D根目录: ${CFL3D_ROOT}"
echo "CFL3D可执行文件: ${CFL3D_EXECUTABLE}"
echo "测试目录: ${TEST_DIR}"
echo "Python脚本: ${PYTHON_SCRIPT}"

echo "=========================================="

# 验证项目根目录
if [ -d "${PROJECT_ROOT}" ]; then
    echo "✅ 项目根目录存在: ${PROJECT_ROOT}"
else
    echo "❌ 项目根目录不存在: ${PROJECT_ROOT}"
fi

# 验证CFL3D根目录
if [ -d "${CFL3D_ROOT}" ]; then
    echo "✅ CFL3D根目录存在: ${CFL3D_ROOT}"
else
    echo "❌ CFL3D根目录不存在: ${CFL3D_ROOT}"
fi

# 验证CFL3D可执行文件
if [ -f "${CFL3D_EXECUTABLE}" ]; then
    echo "✅ CFL3D可执行文件存在: ${CFL3D_EXECUTABLE}"
    echo "   文件信息:"
    ls -la "${CFL3D_EXECUTABLE}"
else
    echo "❌ CFL3D可执行文件不存在: ${CFL3D_EXECUTABLE}"
fi

# 验证测试目录
if [ -d "${TEST_DIR}" ]; then
    echo "✅ 测试目录存在: ${TEST_DIR}"
    echo "   目录内容:"
    ls -la "${TEST_DIR}"
else
    echo "❌ 测试目录不存在: ${TEST_DIR}"
fi

# 验证Python脚本
if [ -f "${PYTHON_SCRIPT}" ]; then
    echo "✅ Python脚本存在: ${PYTHON_SCRIPT}"
else
    echo "❌ Python脚本不存在: ${PYTHON_SCRIPT}"
fi

echo "=========================================="

# 验证相对路径
echo "验证相对路径 (从测试目录到CFL3D可执行文件):"
if [ -d "${TEST_DIR}" ]; then
    cd "${TEST_DIR}"
    RELATIVE_PATH="../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi"
    echo "   当前目录: $(pwd)"
    echo "   相对路径: ${RELATIVE_PATH}"

    if [ -f "${RELATIVE_PATH}" ]; then
        echo "✅ 相对路径正确: ${RELATIVE_PATH}"
        echo "   绝对路径: $(realpath ${RELATIVE_PATH})"
    else
        echo "❌ 相对路径错误: ${RELATIVE_PATH}"
        echo "   尝试查找可执行文件:"
        find ../../ -name "cfl3d_mpi" -type f 2>/dev/null || echo "   未找到cfl3d_mpi文件"
        echo "   目录结构:"
        ls -la ../../ 2>/dev/null || echo "   无法访问上级目录"
    fi
else
    echo "❌ 无法验证相对路径，测试目录不存在"
fi

echo "=========================================="
echo "路径验证完成"
