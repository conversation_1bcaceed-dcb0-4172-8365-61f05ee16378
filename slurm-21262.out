==========================================
SLURM Job ID: 21262
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/CFL3D-SR
==========================================
Loading modules...
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
当前加载的模块:
Currently Loaded Modulefiles:
  1) gcc/11.3.0-gcc-4.8.5   3) intel/oneapi/2022.1
  2) intel/2022.1           4) miniforge/24.1.2
==========================================
Current working directory: /home/<USER>/gpuuser255/lmc/CFL3D-SR/build
==========================================
Cleaning previous build files...
>>> removing all files except makefile in cfl/mpi
(cd cfl/mpi; \
make -f makefile scrub EXEC="cfl3d_mpi" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
rm -f *.a 
rm -f *.o
rm -f cfl3d_mpi 
rm -f *.f
rm -f *.h
rm -f *.F
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
>>> removing all files except makefile in cfl/libs
(cd cfl/libs; \
make -f makefile scrub EXEC="cfl3d_libs" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
rm -f *.a
rm -f *.o *.mod
rm -f cfl3d_libs 
rm -f *.f
rm -f *.h
rm -f *.F *.F90 
rm -f *.c
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
>>> removing all files except makefile in cfl/seq
(cd cfl/seq; \
make -f makefile scrub EXEC="cfl3d_seq" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/seq'
rm -f *.a 
rm -f *.o
rm -f cfl3d_seq 
rm -f *.f
rm -f *.h
rm -f *.F
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/seq'
>>> removing all files except makefile in precfl/seq
(cd precfl/seq; \
make -f makefile scrub EXEC="precfl3d" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/precfl/seq'
rm -f *.o
rm -f precfl3d
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/precfl/seq'
>>> removing all files except makefile in ron/seq
(cd ron/seq; \
make -f makefile scrub EXEC="ronnie" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/ron/seq'
rm -f *.o
rm -f ronnie
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/ron/seq'
>>> removing all files except makefile in mag/seq
(cd mag/seq; \
make -f makefile scrub EXEC="maggie" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/mag/seq'
rm -f *.o
rm -f maggie
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/mag/seq'
>>> removing all files except makefile in split/seq
(cd split/seq; \
make -f makefile scrub EXEC="splitter" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/split/seq'
rm -f *.o
rm -f splitter
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/split/seq'
>>> removing all files except makefile in preron/seq
(cd preron/seq; \
make -f makefile scrub EXEC="preronnie" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/preron/seq'
rm -f *.o
rm -f preronnie
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/preron/seq'
>>> removing all files except makefile in cflcmplx/mpi
(cd cflcmplx/mpi; \
make -f makefile scrub EXEC="cfl3dcmplx_mpi" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/mpi'
rm -f *.a 
rm -f *.o
rm -f cfl3dcmplx_mpi 
rm -f *.f
rm -f *.h
rm -f *.F
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/mpi'
>>> removing all files except makefile in cflcmplx/libs
(cd cflcmplx/libs; \
make -f makefile scrub EXEC="cfl3dcmplx_libs" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/libs'
rm -f *.a
rm -f *.o
rm -f cfl3dcmplx_libs 
rm -f *.f
rm -f *.h
rm -f *.F
rm -f *.c
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/libs'
>>> removing all files except makefile in cflcmplx/seq
(cd cflcmplx/seq; \
make -f makefile scrub EXEC="cfl3dcmplx_seq" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/seq'
rm -f *.a 
rm -f *.o
rm -f cfl3dcmplx_seq 
rm -f *.f
rm -f *.h
rm -f *.F
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/seq'
>>> removing all files except makefile in tools/seq
(cd tools/seq; \
make -f makefile scrub EXEC="cfl3d_tools" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/tools/seq'
rm -f *.a
rm -f *.o
rm -f grid_perturb_cmplx grid_perturb Get_FD moovmaker plot3dg_to_cgns XINTOUT_to_ovrlp p3d_to_INGRID \
 INGRID_to_p3d cfl3d_to_pegbc everyother_xyz initialize_field p3d_to_cfl3drst v6_restart_mod v6inpdoubhalf \
         cgns_to_cfl3dinput cgns_readhist v6_ronnie_mod cfl3d_to_nmf cfl3dinp_to_FVBND nmf_to_cfl3dinput \
         gridswitchijk v6inpswitchijk
rm -f *.f *.F
rm -f *.F *.F90
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/tools/seq'
>>> removing all files except makefile in splitcmplx/seq
(cd splitcmplx/seq; \
make -f makefile scrub EXEC="splittercmplx" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/splitcmplx/seq'
rm -f *.o
rm -f splittercmplx
rm -f *.f
rm -f *.F
rm -f *.h
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/splitcmplx/seq'
Creating symbolic links...
                                                              
==============================================================
  linking common library files for mpi/seq versions of cfl3d  
==============================================================
                                                              
( cd cfl/libs; make -f makefile link \
EXEC="cfl3d_libs" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
        linking source code
ln -sf ../../../source/cfl3d/libs/*.F .
ln -sf ../../../source/cfl3d/libs/*.F90 .
ln -sf ../../../source/cfl3d/libs/*.c .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
                                                              
==============================================================
           linking files for mpi version of cfl3d             
==============================================================
                                                              
( cd cfl/mpi; make -f makefile link \
EXEC="cfl3d_mpi" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
        linking source code
ln -s ../../../source/cfl3d/dist/*.F .
ln -s ../../../source/cfl3d/libs/ccomplex.F .
ln -s ../libs/libcommon.a .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
                                                              
==============================================================
        linking files for sequential version of cfl3d         
==============================================================
                                                              
( cd cfl/seq; make -f makefile link \
EXEC="cfl3d_seq" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/seq'
        linking source code
ln -s ../../../source/cfl3d/dist/*.F .
ln -s ../../../source/cfl3d/libs/ccomplex.F .
ln -s ../libs/libcommon.a .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/seq'
                                                                  
==================================================================
linking common library files for complex mpi/seq versions of cfl3d
==================================================================
                                                                  
( cd cflcmplx/libs; make -f makefile link \
EXEC="cfl3dcmplx_libs" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/libs'
        linking source code
ln -s ../../../source/cfl3d/libs/*.F .
ln -s ../../../source/cfl3d/libs/*.c .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/libs'
                                                              
==============================================================
       linking files for complex mpi version of cfl3d         
==============================================================
                                                              
( cd cflcmplx/mpi; make -f makefile link \
EXEC="cfl3dcmplx_mpi" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/mpi'
        linking source code
ln -s ../../../source/cfl3d/dist/*.F .
ln -s ../../../source/cfl3d/libs/ccomplex.F .
ln -s ../libs/libcommon.a .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/mpi'
                                                              
==============================================================
    linking files for complex sequential version of cfl3d     
==============================================================
                                                              
( cd cflcmplx/seq; make -f makefile link \
EXEC="cfl3dcmplx_seq" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/seq'
        linking source code
ln -s ../../../source/cfl3d/dist/*.F .
ln -s ../../../source/cfl3d/libs/ccomplex.F .
ln -s ../libs/libcommon.a .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cflcmplx/seq'
                                                              
==============================================================
       linking files for sequential version of precfl3d       
==============================================================
                                                              
( cd precfl/seq; make -f makefile link \
EXEC="precfl3d" \
PRESRC="../../../source/precfl3d" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/precfl/seq'
        linking source code
ln -s ../../../source/precfl3d/*.F .
ln -s ../../../source/cfl3d/dist/sizer.F .
ln -s ../../../source/cfl3d/dist/pointers.F .
ln -s ../../../source/cfl3d/dist/compg2n.F .
ln -s ../../../source/cfl3d/dist/umalloc.F .
ln -s ../../../source/cfl3d/dist/termn8.F .
ln -s ../../../source/cfl3d/libs/outbuf.F .
ln -s ../../../source/cfl3d/libs/pre_patch.F .
ln -s ../../../source/cfl3d/libs/pre_blockbc.F .
ln -s ../../../source/cfl3d/libs/pre_period.F .
ln -s ../../../source/cfl3d/libs/getdhdr.F .
ln -s ../../../source/cfl3d/libs/pre_embed.F .
ln -s ../../../source/cfl3d/libs/global.F .
ln -s ../../../source/cfl3d/libs/global2.F .
ln -s ../../../source/cfl3d/libs/rpatch0.F .
ln -s ../../../source/cfl3d/libs/getibk0.F .
ln -s ../../../source/cfl3d/libs/cntsurf.F .
ln -s ../../../source/cfl3d/libs/lead.F .
ln -s ../../../source/cfl3d/libs/global0.F .
ln -s ../../../source/cfl3d/libs/readkey.F .
ln -s ../../../source/cfl3d/libs/parser.F .
ln -s ../../../source/cfl3d/libs/ccomplex.F .
ln -s ../../../source/cfl3d/libs/cgnstools.F .
ln -s ../../../source/cfl3d/libs/setseg.F .
ln -s ../../../source/cfl3d/libs/my_flush.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/precfl/seq'
                                                              
==============================================================
        linking files for sequential version of ronnie        
==============================================================
                                                              
( cd ron/seq; make -f makefile link \
EXEC="ronnie" \
RONSRC="../../../source/ronnie" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/ron/seq'
        linking source code
ln -s  ../../../source/ronnie/*.F .
ln -s  ../../../source/cfl3d/dist/cputim.F .
ln -s  ../../../source/cfl3d/dist/writ_buf.F .
ln -s  ../../../source/cfl3d/dist/umalloc.F .
ln -s  ../../../source/cfl3d/dist/patcher.F .
ln -s  ../../../source/cfl3d/libs/lead.F .
ln -s  ../../../source/cfl3d/libs/pre_patch.F .
ln -s  ../../../source/cfl3d/libs/collx.F .
ln -s  ../../../source/cfl3d/libs/global2.F .
ln -s  ../../../source/cfl3d/libs/arc.F .
ln -s  ../../../source/cfl3d/libs/avgint.F .
ln -s  ../../../source/cfl3d/libs/collapse.F .
ln -s  ../../../source/cfl3d/libs/diagnos.F .
ln -s  ../../../source/cfl3d/libs/direct.F .
ln -s  ../../../source/cfl3d/libs/dsmin.F .
ln -s  ../../../source/cfl3d/libs/expand.F .
ln -s  ../../../source/cfl3d/libs/extra.F .
ln -s  ../../../source/cfl3d/libs/extrae.F .
ln -s  ../../../source/cfl3d/libs/invert.F .
ln -s  ../../../source/cfl3d/libs/loadgr.F .
ln -s  ../../../source/cfl3d/libs/newfit.F .
ln -s  ../../../source/cfl3d/libs/project.F .
ln -s  ../../../source/cfl3d/libs/rechk.F .
ln -s  ../../../source/cfl3d/libs/rp3d.F .
ln -s  ../../../source/cfl3d/libs/shear.F .
ln -s  ../../../source/cfl3d/libs/topol.F .
ln -s  ../../../source/cfl3d/libs/topol2.F .
ln -s  ../../../source/cfl3d/libs/trace.F .
ln -s  ../../../source/cfl3d/libs/xe.F .
ln -s  ../../../source/cfl3d/libs/xe2.F .
ln -s  ../../../source/cfl3d/libs/outbuf.F .
ln -s  ../../../source/cfl3d/libs/transp.F .
ln -s  ../../../source/cfl3d/libs/rotatp.F .
ln -s  ../../../source/cfl3d/libs/ccomplex.F .
ln -s  ../../../source/cfl3d/libs/my_flush.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/ron/seq'
                                                              
==============================================================
        linking files for sequential version of maggie        
==============================================================
                                                              
( cd mag/seq; make -f makefile link \
EXEC="maggie" \
MAGSRC="../../../source/maggie" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/mag/seq'
         linking source code
ln -s ../../../source/maggie/*.F .
         linking header files
ln -s ../../../header/mag1.h .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/mag/seq'
                                                              
==============================================================
    linking files for sequential version of block splitter    
==============================================================
                                                              
( cd split/seq; make -f makefile link \
EXEC="splitter" \
SPLITSRC="../../../source/splitter" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/split/seq'
        linking source code
ln -s ../../../source/splitter/*.F .
ln -s ../../../source/cfl3d/dist/umalloc.F .
ln -s ../../../source/cfl3d/libs/parser.F .
ln -s ../../../source/cfl3d/libs/readkey.F .
ln -s ../../../source/cfl3d/libs/outbuf.F .
ln -s ../../../source/cfl3d/libs/my_flush.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/split/seq'
                                                              
==============================================================
       linking files for sequential version of preronnie      
==============================================================
                                                              
( cd preron/seq; make -f makefile link \
EXEC="preronnie" \
PRERONSRC="../../../source/ronnie" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/preron/seq'
        linking source code
ln -s  ../../../source/ronnie/main_pre.F .
ln -s  ../../../source/ronnie/termn8.F .
ln -s  ../../../source/ronnie/usrint.F .
ln -s  ../../../source/ronnie/sizer.F .
ln -s  ../../../source/cfl3d/dist/umalloc.F .
ln -s  ../../../source/cfl3d/libs/global2.F .
ln -s  ../../../source/cfl3d/libs/outbuf.F .
ln -s  ../../../source/cfl3d/libs/ccomplex.F .
ln -s  ../../../source/cfl3d/libs/my_flush.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/preron/seq'
                                                              
==============================================================
 linking files for sequential versions of some cfl3d utilities
==============================================================
                                                              
( cd tools/seq; make -f makefile link \
EXEC="cfl3d_tools" \
TOOLSSRC="../../../source/tools" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/tools/seq'
        linking source code
ln -s ../../../source/tools/*.F .
ln -s ../../../source/tools/*.F90 .
ln -s ../../../source/cfl3d/dist/umalloc_r.F .
ln -s ../../../source/cfl3d/dist/umalloc_c.F .
ln -s ../../../source/cfl3d/libs/readkey.F .
ln -s ../../../source/cfl3d/libs/global0.F .
ln -s ../../../source/cfl3d/libs/parser.F .
ln -s ../../../source/cfl3d/libs/outbuf.F .
ln -s ../../../source/cfl3d/libs/cgnstools.F  .
ln -s  ../../../source/cfl3d/libs/my_flush.F .
ln -s ../../../source/cfl3d/dist/termn8.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/tools/seq'
                                                              
==============================================================
linking files for complex sequential version of block splitter
==============================================================
                                                              
( cd splitcmplx/seq; make -f makefile link \
EXEC="splittercmplx" \
SPLITSRC="../../../source/splitter" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/splitcmplx/seq'
        linking source code
ln -s ../../../source/splitter/*.F .
ln -s ../../../source/cfl3d/dist/umalloc.F .
ln -s ../../../source/cfl3d/dist/umalloc_c.F .
ln -s ../../../source/cfl3d/libs/parser.F .
ln -s ../../../source/cfl3d/libs/readkey.F .
ln -s ../../../source/cfl3d/libs/outbuf.F .
ln -s ../../../source/cfl3d/libs/my_flush.F .
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/splitcmplx/seq'
Building CFL3D libraries...
                                                              
==============================================================
                    compiling cfl3d_libs                 
==============================================================
                                                              
( cd cfl/libs; make -f makefile cfl3d_libs \
FFLAG="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8 " \
FFLAG_SPEC="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs -r8 " \
LFLAG="-z muldefs -xHost -traceback -fpe0" \
EXEC="cfl3d_libs" \
CPPFLAG="-P" \
CPPOPT="-DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN  " \
AROPT="rusc" \
RANLIB="true" \
CPP="cpp" \
CC="mpiicc" \
CFLAG="" \
LLIBS="" \
FTN="mpiifort" \
DIR="cfl/libs" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
make[1]: warning: jobserver unavailable: using -j1.  Add `+' to parent make rule.
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c abciz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c abcjz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c abckz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c add2x.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c aesurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ae_pred.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c af3f.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c amafi.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c amafj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c amafk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c arc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c arclen.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c augmntq.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c avghole.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c avgint.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c barth3d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1000.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1001.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1002.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1003.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1005.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1008.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1011.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1012.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc1013.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2002.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2003.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2004.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c module_stm_2005.F90
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2005.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2005i_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2005j_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2005k_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2006.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2007.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2008.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2009.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2010.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2016.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2019.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2026.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc2102.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc9999.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bcchk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bcnonin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_delt.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_info.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_vdsp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_xmera.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blkmax.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blnkfr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blocki.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blocki_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blockj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blockj_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blockk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blockk_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c blomax.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bsub.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bsubp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblki.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblki_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblkj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblkj_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblkk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cblkk_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ccf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ccomplex.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cctogp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cellvol.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cgnstools.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkdef.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkrap.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkrot.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkroti_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkrotj_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chkrotk_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c chksym.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cntsurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c coll2q.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collapse.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c colldat.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collmod.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collq.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collqc0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collx.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collxt.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c collxtb.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c csout.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c csurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ctime1.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dabciz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dabcjz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dabckz.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c deform.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c delintr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c delq.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c delv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c delv_k.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c delv_p.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dfbtr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dfbtrp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dfhat.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dfluxpm.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c diagi.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c diagj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c diagk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c diagnos.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dird.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c direct.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dlutr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dlutrp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dsmin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dthole.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c expand.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c extra.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c extrae.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fa.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fa2xi.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fa2xj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fa2xk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fcd.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fdelay.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ffluxl.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ffluxr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ffluxr_cd.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ffluxv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ffluxv1.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fhat.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fill.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fluxm.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fluxp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c fmaps.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c force.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c foureqn.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c genforce.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c getdelt.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c getdhdr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c getibk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c getibk0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c getsurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c get_bvals.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gfluxl.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gfluxr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gfluxr_cd.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gfluxv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gfluxv1.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c global.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c global0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c global2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c gradinfo.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c grdmove.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hfluxl.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hfluxr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hfluxr_cd.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hfluxv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hfluxv1.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c histout.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c hole.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2x.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xi_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xj_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xk_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xs.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xsi_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xsj_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c i2xsk_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c module_profileout.F90
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c module_kwstm.F90
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c init.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c initnonin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c initvist.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c init_ae.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c init_mast.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c init_rb.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c init_trim.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c int2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c int2_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c intrbc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c invert.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c l2norm.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c l2norm2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c lamfix.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ld_dati.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ld_datj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ld_datk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ld_qc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c lead.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c lesdiag.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c loadgr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c metric.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c mms.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c moddefl.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c modread.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c mreal.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c mvdat.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c my_flush.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c newfit.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c outbuf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c parser.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pltmode.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_blockbc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_blocki.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_blockj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_blockk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_cblki.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_cblkj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_cblkk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_embed.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_patch.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_period.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c project.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c prolim.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c prolim2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c q8sdot.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c q8smax.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c q8smin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c q8vrev.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qface.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rb_corr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rb_pred.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rcfl.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c readdat.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c readkey.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rechk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c resadd.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c resid.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c resnonin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rie1d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rie1de.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotate.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotateq.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotateq0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotateq2_d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotateqb.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotatmc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotatp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rotsurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rp3d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rpatch.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rpatch0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rsmooth.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rsurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setblk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setcorner.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setdqc0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setqc0.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setseg.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c shear.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c sijrate2d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c sijrate3d.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c spalart.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c swafi.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c swafj.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c swafk.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tau.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tau2x.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tdq.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tfiedge.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tfiface.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tfivol.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c threeeqn.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tinvr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c tmetric.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c topol.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c topol2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c trace.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c trans.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c transmc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c transp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c triv.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c trnsurf.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c twoeqn.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c unld_qc.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c update.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c u_doubleprime.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c varnam.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c vargrad.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c vlutr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c vlutrp.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c wkstn.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c wmag.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xe.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xe2.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xlim.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xlsfree.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xmukin.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xtbatb.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xupdt.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c xyzintr.F
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs -r8  -c addx.F
mpiicc  -c bessel.c
mpiifort -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c module_contour.F90
ar rusc libcommon.a abciz.o abcjz.o abckz.o add2x.o aesurf.o ae_pred.o af3f.o amafi.o amafj.o amafk.o arc.o arclen.o augmntq.o avghole.o avgint.o barth3d.o bc.o bc1000.o bc1001.o bc1002.o bc1003.o bc1005.o bc1008.o bc1011.o bc1012.o bc1013.o bc2002.o bc2003.o bc2004.o bc2005.o bc2005i_d.o bc2005j_d.o bc2005k_d.o bc2006.o bc2007.o bc2008.o bc2009.o bc2010.o bc2016.o bc2019.o bc2026.o bc2102.o bc9999.o bcchk.o bcnonin.o bc_delt.o bc_info.o bc_vdsp.o bc_xmera.o blkmax.o blnkfr.o blocki.o blocki_d.o blockj.o blockj_d.o blockk.o blockk_d.o blomax.o bsub.o bsubp.o cblki.o cblki_d.o cblkj.o cblkj_d.o cblkk.o cblkk_d.o ccf.o ccomplex.o cctogp.o cellvol.o cgnstools.o chkdef.o chkrap.o chkrot.o chkroti_d.o chkrotj_d.o chkrotk_d.o chksym.o cntsurf.o coll2q.o collapse.o colldat.o collmod.o collq.o collqc0.o collv.o collx.o collxt.o collxtb.o csout.o csurf.o ctime1.o dabciz.o dabcjz.o dabckz.o deform.o delintr.o delq.o delv.o delv_k.o delv_p.o dfbtr.o dfbtrp.o dfhat.o dfluxpm.o diagi.o diagj.o diagk.o diagnos.o dird.o direct.o dlutr.o dlutrp.o dsmin.o dthole.o expand.o extra.o extrae.o fa.o fa2xi.o fa2xj.o fa2xk.o fcd.o fdelay.o ffluxl.o ffluxr.o ffluxr_cd.o ffluxv.o ffluxv1.o fhat.o fill.o fluxm.o fluxp.o fmaps.o force.o foureqn.o genforce.o getdelt.o getdhdr.o getibk.o getibk0.o getsurf.o get_bvals.o gfluxl.o gfluxr.o gfluxr_cd.o gfluxv.o gfluxv1.o global.o global0.o global2.o gradinfo.o grdmove.o hfluxl.o hfluxr.o hfluxr_cd.o hfluxv.o hfluxv1.o histout.o hole.o i2x.o i2xi_d.o i2xj_d.o i2xk_d.o i2xs.o i2xsi_d.o i2xsj_d.o i2xsk_d.o init.o initnonin.o initvist.o init_ae.o init_mast.o init_rb.o init_trim.o int2.o int2_d.o intrbc.o invert.o l2norm.o l2norm2.o lamfix.o ld_dati.o ld_datj.o ld_datk.o ld_qc.o lead.o lesdiag.o loadgr.o metric.o mms.o moddefl.o modread.o mreal.o mvdat.o my_flush.o newfit.o outbuf.o parser.o pltmode.o pre_blockbc.o pre_blocki.o pre_blockj.o pre_blockk.o pre_cblki.o pre_cblkj.o pre_cblkk.o pre_embed.o pre_patch.o pre_period.o project.o prolim.o prolim2.o q8sdot.o q8smax.o q8smin.o q8vrev.o qface.o rb_corr.o rb_pred.o rcfl.o readdat.o readkey.o rechk.o resadd.o resid.o resnonin.o rie1d.o rie1de.o rotate.o rotateq.o rotateq0.o rotateq2_d.o rotateqb.o rotatmc.o rotatp.o rotsurf.o rp3d.o rpatch.o rpatch0.o rsmooth.o rsurf.o setblk.o setcorner.o setdqc0.o setqc0.o setseg.o shear.o sijrate2d.o sijrate3d.o spalart.o swafi.o swafj.o swafk.o tau.o tau2x.o tdq.o tfiedge.o tfiface.o tfivol.o threeeqn.o tinvr.o tmetric.o topol.o topol2.o trace.o trans.o transmc.o transp.o triv.o trnsurf.o twoeqn.o unld_qc.o update.o u_doubleprime.o varnam.o vargrad.o vlutr.o vlutrp.o wkstn.o wmag.o xe.o xe2.o xlim.o xlsfree.o xmukin.o xtbatb.o xupdt.o xyzintr.o module_profileout.o module_contour.o module_kwstm.o module_stm_2005.o  addx.o bessel.o 
                                                              
==============================================================
                                                              
               DONE:  libcommon.a created                    
                                                              
           the archive libraries can be found in:             
                                                              
                   cfl/libs/libcommon.a                        
                                                              
==============================================================
                                                              
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
Building CFL3D MPI version...
                                                              
                                                              
==============================================================
==============================================================
                    compiling cfl3d_libs                 
                    compiling cfl3d_mpi                 
==============================================================
==============================================================
                                                              
                                                              
( cd cfl/mpi; make -f makefile cfl3d_mpi \
FFLAG="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8 " \
FFLAG_SPEC="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs -r8 " \
LFLAG="-z muldefs -xHost -traceback -fpe0" \
EXEC="cfl3d_mpi" \
CPPFLAG="-P" \
CPPOPT=" -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN  " \
AROPT="rusc" \
RANLIB="true" \
CPP="cpp" \
LLIBS="" \
FTN="mpiifort" \
DIR="cfl/mpi" \
CFLSRC_D="../../../source/cfl3d/dist" \
CFLSRC_S="../../../source/cfl3d/libs" \
HEADER="../../../header" )
( cd cfl/libs; make -f makefile cfl3d_libs \
FFLAG="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8 " \
FFLAG_SPEC="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs -r8 " \
LFLAG="-z muldefs -xHost -traceback -fpe0" \
EXEC="cfl3d_libs" \
CPPFLAG="-P" \
CPPOPT="-DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN  " \
AROPT="rusc" \
RANLIB="true" \
CPP="cpp" \
CC="mpiicc" \
CFLAG="" \
LLIBS="" \
FTN="mpiifort" \
DIR="cfl/libs" \
CFLSRC_S="../../../source/cfl3d/libs" \
CFLSRC_D="../../../source/cfl3d/dist" \
HEADER="../../../header" )
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
make[1]: warning: jobserver unavailable: using -j1.  Add `+' to parent make rule.
make[1]: Entering directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
make[1]: warning: jobserver unavailable: using -j1.  Add `+' to parent make rule.
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ccomplex.F
                                                              
==============================================================
                                                              
               DONE:  libcommon.a created                    
                                                              
           the archive libraries can be found in:             
                                                              
                   cfl/libs/libcommon.a                        
                                                              
==============================================================
                                                              
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/libs'
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c development.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c main.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c ae_corr.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c avggp.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_blkint.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_embed.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_patch.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c bc_period.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c calyplus.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cfl3d.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c compg2n.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c cputim.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c dynptch.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c findmin_new.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c forceout.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c mgbl.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c mgblk.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c newalpha.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c patcher.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3c.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3c_sample.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3d_avg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3d.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3d_2d.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3d_coarse.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c plot3t.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pointers.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c pre_bc.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c prntcp.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qinter.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qout.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qout_2d.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qout_avg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qout_coarse.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c qout_sample.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c reass.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c resetg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c resp.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rrest.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c rrestg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setslave.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c setup.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c sizer.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c termn8.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c trnsfr_vals.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c umalloc.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c umalloc_c.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c umalloc_r.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c updatedg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c updateg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c usrint.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c wrest.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c wrestg.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c writ_buf.F
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8  -c yplusout.F
ar rusc libdist.a ae_corr.o avggp.o bc_blkint.o bc_embed.o bc_patch.o bc_period.o calyplus.o cfl3d.o compg2n.o cputim.o dynptch.o findmin_new.o forceout.o mgbl.o mgblk.o newalpha.o patcher.o plot3c.o plot3c_sample.o plot3d_avg.o plot3d.o plot3d_2d.o plot3d_coarse.o plot3t.o pointers.o pre_bc.o prntcp.o qinter.o qout.o qout_2d.o qout_avg.o qout_coarse.o qout_sample.o reass.o resetg.o resp.o rrest.o rrestg.o setslave.o setup.o sizer.o termn8.o trnsfr_vals.o umalloc.o umalloc_c.o umalloc_r.o updatedg.o updateg.o usrint.o wrest.o wrestg.o writ_buf.o yplusout.o 
ln -s ../libs/*.o .
ln: failed to create symbolic link ‘./ccomplex.o’: File exists
make[1]: [cfl3d_mpi] Error 1 (ignored)
mpiifort -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN   -z muldefs -xHost -traceback -fpe0 -o cfl3d_mpi *.o  
                                                              
==============================================================
                                                              
                   DONE:  cfl3d_mpi created                     
                                                              
              the mpi executable can be found in:             
                                                              
                    cfl/mpi/cfl3d_mpi                            
                                                              
==============================================================
                                                              
make[1]: Leaving directory `/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi'
==========================================
Build completed!
Checking if executable was created...
✅ SUCCESS: cfl3d_mpi executable created successfully!
Location: /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
-rwxrwxr-x 1 <USER> <GROUP> 12885448 May 26 13:53 cfl/mpi/cfl3d_mpi
==========================================
Build job completed at Mon May 26 13:53:21 CST 2025
