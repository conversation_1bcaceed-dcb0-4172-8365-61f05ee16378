#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import subprocess
import time

def main():
    print("--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---")

    # Assume this script is run from the project root (e.g., Agent-R1-q3)
    project_root = os.getcwd()
    cfl3d_test_dir = os.path.join(project_root, "cfl3d_test")

    print(f"[Info] Project root (assumed): {project_root}")
    print(f"[Info] Target execution directory (cfl3d_test): {cfl3d_test_dir}")

    if not os.path.isdir(cfl3d_test_dir):
        print(f"[Error] cfl3d_test directory NOT FOUND at: {cfl3d_test_dir}")
        print("        Please ensure this directory exists and contains the necessary input files.")
        return

    # This is the path to cfl3d_mpi RELATIVE TO THE cfl3d_test_dir
    # From /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
    # To   /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
    # Relative path: ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
    relative_path_to_cfl3d_mpi_from_test_dir = '../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi'

    # Verify the executable path if we were to resolve it from script's CWD for mpirun's perspective
    # mpirun itself will handle this path relative to its CWD (which will be cfl3d_test_dir)
    # For simple verification, we can check its existence from the perspective of cfl3d_test_dir
    abs_path_for_verification = os.path.abspath(os.path.join(cfl3d_test_dir, relative_path_to_cfl3d_mpi_from_test_dir))
    if not os.path.exists(abs_path_for_verification):
        print(f"[Warning] Verified absolute path {abs_path_for_verification} (derived from relative path within cfl3d_test) does not exist.")
        print("           Continuing, as mpirun will resolve it from its CWD.")
    else:
        print(f"[Info] Verified absolute path {abs_path_for_verification} (derived from relative path within cfl3d_test) exists.")


    num_processes = 2
    # The command mpirun will execute. The executable path is relative to cfl3d_test_dir.
    mpi_command = ['mpirun', '-np', str(num_processes), relative_path_to_cfl3d_mpi_from_test_dir]

    cmd_input_for_cfl3d = 'y\n'  # Standard input often required by cfl3d_mpi

    print(f"[Exec] Executing command: {' '.join(mpi_command)}")
    print(f"        with CWD: {cfl3d_test_dir}")
    print(f"        with STDIN: '{cmd_input_for_cfl3d.strip()}'")

    start_time = time.time()
    process = None # Initialize process to None
    try:
        process = subprocess.Popen(
            mpi_command,
            cwd=cfl3d_test_dir, # CRITICAL: Run mpirun from within cfl3d_test_dir
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        stdout_bytes, stderr_bytes = process.communicate(
            input=cmd_input_for_cfl3d.encode('ascii'),
            timeout=600  # 10-minute timeout
        )

        return_code = process.returncode
        stdout = stdout_bytes.decode('latin-1', errors='ignore')
        stderr = stderr_bytes.decode('latin-1', errors='ignore')

        print(f"[Result] Subprocess finished in {time.time() - start_time:.2f} seconds.")
        print(f"  Return Code: {return_code}")

        if stdout:
            print("  STDOUT:")
            print(stdout)
        else:
            print("  STDOUT: (empty)")

        if stderr:
            print("  STDERR:")
            print(stderr)
        else:
            print("  STDERR: (empty)")

    except FileNotFoundError as fnf_error:
        print(f"[FATAL ERROR] FileNotFoundError during Popen: {fnf_error}")
        print("        This usually means 'mpirun' itself was not found in PATH, or the specified executable part of the command was not found by mpirun.")
    except subprocess.TimeoutExpired:
        print("[FATAL ERROR] Subprocess timed out after 600 seconds.")
        if process: # Check if process was initialized
            process.kill()
            stdout_bytes, stderr_bytes = process.communicate() # Drain pipes
            stdout = stdout_bytes.decode('latin-1', errors='ignore')
            stderr = stderr_bytes.decode('latin-1', errors='ignore')
            print("  Last STDOUT before kill:")
            print(stdout)
            print("  Last STDERR before kill:")
            print(stderr)
    except Exception as e:
        print(f"[FATAL ERROR] An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("--- Direct CFL3D run in cfl3d_test_dir finished ---")

if __name__ == "__main__":
    main()