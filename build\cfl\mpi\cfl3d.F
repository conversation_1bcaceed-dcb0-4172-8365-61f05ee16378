c  ---------------------------------------------------------------------------
c  CFL3D is a structured-grid, cell-centered, upwind-biased, Reynolds-averaged
c  Navier-Stokes (RANS) code. It can be run in parallel on multiple grid zones
c  with point-matched, patched, overset, or embedded connectivities. Both
c  multigrid and mesh sequencing are available in time-accurate or
c  steady-state modes.
c
c  Copyright 2001 United States Government as represented by the Administrator
c  of the National Aeronautics and Space Administration. All Rights Reserved.
c
c  The CFL3D platform is licensed under the Apache License, Version 2.0
c  (the "License"); you may not use this file except in compliance with the
c  License. You may obtain a copy of the License at
c  http://www.apache.org/licenses/LICENSE-2.0.
c
c  Unless required by applicable law or agreed to in writing, software
c  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
c  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
c  License for the specific language governing permissions and limitations
c  under the License.
c  ---------------------------------------------------------------------------
c
      subroutine cfl3d(mwork,mworki,nplots,minnode,iitot,intmax,
     .                 maxxe,mxbli,nsub1,lbcprd,lbcemb,lbcrad,maxbl,
     .                 maxgr,maxseg,maxcs,ncycmax,intmx,mxxe,mptch,
     .                 msub1,ibufdim,nbuf,mxbcfil,istat_size,ntr,
     .                 nmds,maxaes,nslave,maxsegdg,nmaster,bcfiles,
     .                 bou,nou,grid,n_clcd,nblocks_clcd,blocks_clcd,
     .                 nvdsp,maxsw,maxsmp)
c
c     $Id$
c
c***********************************************************************
c     Purpose:  Allocate memory and execute flow solver
c***********************************************************************
c
#   ifdef CMPLX
      implicit complex(a-h,o-z)
#   endif
c
#if defined DIST_MPI
c
#     include "mpif.h"
#   ifdef DBLE_PRECSN
#      ifdef CMPLX
#        define MY_MPI_REAL MPI_DOUBLE_COMPLEX
#      else
#        define MY_MPI_REAL MPI_DOUBLE_PRECISION
#      endif
#   else
#      ifdef CMPLX
#        define MY_MPI_REAL MPI_COMPLEX
#      else
#        define MY_MPI_REAL MPI_REAL
#      endif
#   endif
      dimension istat(MPI_STATUS_SIZE)
#endif
c
      character*80 bcfiles(mxbcfil)
      character*120 bou(ibufdim,nbuf)
      character*32 basedesired
      character*80 grid
c
      integer lout,xif1,xif2,etf1,etf2
      integer bcfilei,bcfilej,bcfilek
      integer stats
c
      dimension nou(nbuf)
c
      allocatable :: aehist(:,:,:,:)
      allocatable :: aesrfdat(:,:)
      allocatable :: bcfilei(:,:,:)
      allocatable :: bcfilej(:,:,:)
      allocatable :: bcfilek(:,:,:)
      allocatable :: bcvali(:,:,:,:)
      allocatable :: bcvalj(:,:,:,:)
      allocatable :: bcvalk(:,:,:,:)
      allocatable :: bmat(:,:,:)
      allocatable :: cdpt(:)
      allocatable :: cdpw(:)
      allocatable :: cdt(:)
      allocatable :: cdvt(:)
      allocatable :: cdvw(:)
      allocatable :: cdw(:)
      allocatable :: cfdmom(:)
      allocatable :: cfdp(:)
      allocatable :: cfdtot(:)
      allocatable :: cfdv(:)
      allocatable :: cflmom(:)
      allocatable :: cflp(:)
      allocatable :: cfltot(:)
      allocatable :: cflv(:)
      allocatable :: cftmom(:)
      allocatable :: cftmomw(:)
      allocatable :: cftp(:)
      allocatable :: cftpw(:)
      allocatable :: cfttot(:)
      allocatable :: cfttotw(:)
      allocatable :: cftv(:)
      allocatable :: cftvw(:)
      allocatable :: cfxmom(:)
      allocatable :: cfxp(:)
      allocatable :: cfxtot(:)
      allocatable :: cfxv(:)
      allocatable :: cfymom(:)
      allocatable :: cfyp(:)
      allocatable :: cfytot(:)
      allocatable :: cfyv(:)
      allocatable :: cfzmom(:)
      allocatable :: cfzp(:)
      allocatable :: cfztot(:)
      allocatable :: cfzv(:)
      allocatable :: chdw(:)
      allocatable :: clt(:)
      allocatable :: clw(:)
      allocatable :: cmxt(:)
      allocatable :: cmxw(:)
      allocatable :: cmyt(:)
      allocatable :: cmyw(:)
      allocatable :: cmzt(:)
      allocatable :: cmzw(:)
      allocatable :: clcd(:,:,:)
      allocatable :: cxt(:)
      allocatable :: cxw(:)
      allocatable :: cyt(:)
      allocatable :: cyw(:)
      allocatable :: czt(:)
      allocatable :: czw(:)
      allocatable :: damp(:,:)
      allocatable :: dthetx(:,:)
      allocatable :: dthetxx(:,:)
      allocatable :: dthety(:,:)
      allocatable :: dthetyy(:,:)
      allocatable :: dthetz(:,:)
      allocatable :: dthetzz(:,:)
      allocatable :: dthxmx(:)
      allocatable :: dthymx(:)
      allocatable :: dthzmx(:)
      allocatable :: dx(:,:)
      allocatable :: dxintg(:)
      allocatable :: dxmx(:)
      allocatable :: dy(:,:)
      allocatable :: dyintg(:)
      allocatable :: dymx(:)
      allocatable :: dz(:,:)
      allocatable :: dzintg(:)
      allocatable :: dzmx(:)
      allocatable :: eta2s(:,:)
      allocatable :: etf1(:)
      allocatable :: etf2(:)
      allocatable :: factjhi(:,:)
      allocatable :: factjlo(:,:)
      allocatable :: factkhi(:,:)
      allocatable :: factklo(:,:)
      allocatable :: fmdot(:)
      allocatable :: fmdotw(:)
      allocatable :: freq(:,:)
      allocatable :: geom_miss(:)
      allocatable :: gf0(:,:)
      allocatable :: gforcn(:,:)
      allocatable :: gforcnm(:,:)
      allocatable :: gforcs(:,:)
      allocatable :: gmass(:,:)
      allocatable :: iadvance(:)
      allocatable :: iaesurf(:,:)
      allocatable :: ibcg(:)
      allocatable :: ibcinfo(:,:,:,:)
      allocatable :: ibpntsg(:,:)
      allocatable :: icouple(:,:)
      allocatable :: icsf(:,:)
      allocatable :: icsi(:,:)
      allocatable :: icsinfo(:,:)
      allocatable :: idefrm(:)
      allocatable :: idegg(:,:)
      allocatable :: idfrmseg(:,:)
      allocatable :: idiagg(:,:)
      allocatable :: idimg(:)
      allocatable :: ieg(:)
      allocatable :: iemg(:)
      allocatable :: ifdsg(:,:)
      allocatable :: ifiner(:)
      allocatable :: iflimg(:,:)
      allocatable :: iforce(:)
      allocatable :: ifrom(:)
      allocatable :: igridg(:)
      allocatable :: iibg(:)
      allocatable :: iic0(:)
      allocatable :: iifit(:)
      allocatable :: iiig(:)
      allocatable :: iiint1(:)
      allocatable :: iiint2(:)
      allocatable :: iindex(:,:)
      allocatable :: iindx(:,:)
      allocatable :: iiorph(:)
      allocatable :: iipntsg(:)
      allocatable :: iitmax(:)
      allocatable :: iitoss(:)
      allocatable :: ilamhig(:)
      allocatable :: ilamlog(:)
      allocatable :: imx(:)
      allocatable :: inewgg(:)
      allocatable :: inpl3d(:,:)
      allocatable :: inpr(:,:)
      allocatable :: iovrlp(:)
      allocatable :: irotat(:)
      allocatable :: isav_blk(:,:)
      allocatable :: isav_dpat(:,:)
      allocatable :: isav_dpat_b(:,:,:)
      allocatable :: isav_emb(:,:)
      allocatable :: isav_pat(:,:)
      allocatable :: isav_pat_b(:,:,:)
      allocatable :: isav_prd(:,:)
      allocatable :: isg(:)
      allocatable :: iskip(:,:)
      allocatable :: istat2_bl(:,:)
      allocatable :: istat2_em(:,:)
      allocatable :: istat2_pa(:,:)
      allocatable :: istat2_pe(:,:)
      allocatable :: isva(:,:,:)
      allocatable :: itrans(:)
      allocatable :: iviscg(:,:)
      allocatable :: ivmax(:)
      allocatable :: iwfg(:,:)
      allocatable :: iwork(:)
      allocatable :: jbcinfo(:,:,:,:)
      allocatable :: jcsf(:,:)
      allocatable :: jcsi(:,:)
      allocatable :: jdimg(:)
      allocatable :: jeg(:)
      allocatable :: jimage(:,:,:)
      allocatable :: jjbg(:)
      allocatable :: jjig(:)
      allocatable :: jjmax1(:)
      allocatable :: jlamhig(:)
      allocatable :: jlamlog(:)
      allocatable :: jmm(:)
      allocatable :: jmx(:)
      allocatable :: jsg(:)
      allocatable :: jskip(:,:)
      allocatable :: jte(:)
      allocatable :: jvmax(:)
      allocatable :: kbcinfo(:,:,:,:)
      allocatable :: kcsf(:,:)
      allocatable :: kcsi(:,:)
      allocatable :: kdimg(:)
      allocatable :: keg(:)
      allocatable :: kimage(:,:,:)
      allocatable :: kkbg(:)
      allocatable :: kkig(:)
      allocatable :: kkmax1(:)
      allocatable :: klamhig(:)
      allocatable :: klamlog(:)
      allocatable :: kmm(:)
      allocatable :: kmx(:)
      allocatable :: ksg(:)
      allocatable :: kskip(:,:)
      allocatable :: kte(:)
      allocatable :: kvmax(:)
      allocatable :: lbg(:)
      allocatable :: levelg(:)
      allocatable :: lig(:)
      allocatable :: limblk(:,:,:)
      allocatable :: llimit(:)
      allocatable :: lout(:)
      allocatable :: lw(:,:)
      allocatable :: lw2(:,:)
      allocatable :: lwdat(:,:,:)
      allocatable :: mblk2nd(:)
      allocatable :: mblkpt(:)
      allocatable :: mit(:,:)
      allocatable :: mmceta(:)
      allocatable :: mmcxie(:)
      allocatable :: nbci0(:)
      allocatable :: nbcidim(:)
      allocatable :: nbcj0(:)
      allocatable :: nbcjdim(:)
      allocatable :: nbck0(:)
      allocatable :: nbckdim(:)
      allocatable :: nblcg(:)
      allocatable :: nblg(:)
      allocatable :: nblk(:,:)
      allocatable :: nblk1(:)
      allocatable :: nblk2(:)
      allocatable :: nblkpt(:)
      allocatable :: nblon(:)
      allocatable :: ncgg(:)
      allocatable :: ncheck(:)
      allocatable :: nsegdfrm(:)
      allocatable :: omegax(:)
      allocatable :: omegay(:)
      allocatable :: omegaz(:)
      allocatable :: omgxae(:,:)
      allocatable :: omgyae(:,:)
      allocatable :: omgzae(:,:)
      allocatable :: pav(:)
      allocatable :: period_miss(:)
      allocatable :: perturb(:,:,:)
      allocatable :: ptav(:)
      allocatable :: qb(:,:,:)
      allocatable :: resmx(:)
      allocatable :: rfreqr(:)
      allocatable :: rfreqt(:)
      allocatable :: rfrqrae(:,:)
      allocatable :: rfrqtae(:,:)
      allocatable :: rkap0g(:,:)
      allocatable :: rms(:)
      allocatable :: seta(:,:,:)
      allocatable :: seta2(:,:)
      allocatable :: islavept(:,:,:)
      allocatable :: nblelst(:,:)
      allocatable :: iskmax(:)
      allocatable :: jskmax(:)
      allocatable :: kskmax(:)
      allocatable :: ue(:)
      allocatable :: stm(:,:,:)
      allocatable :: stmi(:,:,:)
      allocatable :: stot(:)
      allocatable :: swett(:)
      allocatable :: swetw(:)
      allocatable :: sx(:)
      allocatable :: sxie(:,:,:)
      allocatable :: sxie2(:,:)
      allocatable :: sy(:)
      allocatable :: sz(:)
      allocatable :: tav(:)
      allocatable :: temp(:)
      allocatable :: thetax(:)
      allocatable :: thetaxl(:)
      allocatable :: thetay(:)
      allocatable :: thetayl(:)
      allocatable :: thetaz(:)
      allocatable :: thetazl(:)
      allocatable :: thtxae(:,:)
      allocatable :: thtyae(:,:)
      allocatable :: thtzae(:,:)
      allocatable :: time2(:)
      allocatable :: timekeep(:)
      allocatable :: ttav(:)
      allocatable :: utrans(:)
      allocatable :: utrnsae(:,:)
      allocatable :: vormax(:)
      allocatable :: vtrans(:)
      allocatable :: vtrnsae(:,:)
      allocatable :: windex(:,:)
      allocatable :: windx(:,:)
      allocatable :: work(:)
      allocatable :: wtrans(:)
      allocatable :: wtrnsae(:,:)
      allocatable :: x0(:,:)
      allocatable :: x1(:,:)
      allocatable :: x2(:,:)
      allocatable :: xie2s(:,:)
      allocatable :: xif1(:)
      allocatable :: xif2(:)
      allocatable :: xmav(:)
      allocatable :: xmi(:,:,:)
      allocatable :: xmie(:,:,:)
      allocatable :: xorgae(:,:)
      allocatable :: xorgae0(:,:)
      allocatable :: xorig(:)
      allocatable :: xorig0(:)
      allocatable :: xs(:,:)
      allocatable :: xte(:,:,:)
      allocatable :: xxn(:,:)
      allocatable :: y1(:,:)
      allocatable :: y2(:,:)
      allocatable :: ymi(:,:,:)
      allocatable :: ymie(:,:,:)
      allocatable :: yorgae(:,:)
      allocatable :: yorgae0(:,:)
      allocatable :: yorig(:)
      allocatable :: yorig0(:)
      allocatable :: yte(:,:,:)
      allocatable :: z1(:,:)
      allocatable :: z2(:,:)
      allocatable :: zmi(:,:,:)
      allocatable :: zmie(:,:,:)
      allocatable :: zorgae(:,:)
      allocatable :: zorgae0(:,:)
      allocatable :: zorig(:)
      allocatable :: zorig0(:)
      allocatable :: zte(:,:,:)
      allocatable :: irdrea(:)
      allocatable :: iswinfo(:,:)
      allocatable :: ismpinfo(:,:)
c
      integer n_clcd, nblocks_clcd
      integer, dimension(2,n_clcd) :: blocks_clcd

      common /ginfo2/ lq2avg,iskip_blocks,inc_2d(3),inc_coarse(3),
     .        lqsavg,lqs2avg,lvdsp,lvdj0,lvdk0,lvdi0,lvdavg,lvd2avg,
     .        lvsj0,lvsjdim,lvsk0,lvskdim,lvsi0,lvsidim
      common /mydist2/ nnodes,myhost,myid,mycomm
      common /deformz/ beta1,beta2,alpha1,alpha2,isktyp,negvol,meshdef,
     .                 nsprgit,ndgrd,ndwrt
      common /cgns/ icgns,iccg,ibase,nzones,nsoluse,irind,jrind,krind
      common /unit5/ iunit5
      common /wrestq/ irest,irest2
c
c***********************************************************************
c     memory allocation
c***********************************************************************
c
      memuse = 0
c
      allocate( aehist(ncycmax,3,nmds,maxaes),stat=stats )
      call umalloc(ncycmax*3*nmds*maxaes,0,'aehist',memuse,stats)
      allocate( aesrfdat(5,maxaes),stat=stats )
      call umalloc(5*maxaes,0,'aesrfdat',memuse,stats)
      allocate( bcfilei(maxbl,maxseg,2),stat=stats )
      call umalloc(maxbl*maxseg*2,1,'bcfilei',memuse,stats)
      allocate( bcfilej(maxbl,maxseg,2),stat=stats )
      call umalloc(maxbl*maxseg*2,1,'bcfilej',memuse,stats)
      allocate( bcfilek(maxbl,maxseg,2),stat=stats )
      call umalloc(maxbl*maxseg*2,1,'bcfilek',memuse,stats)
      allocate( bcvali(maxbl,maxseg,12,2),stat=stats )
      call umalloc(maxbl*maxseg*12*2,0,'bcvali',memuse,stats)
      allocate( bcvalj(maxbl,maxseg,12,2),stat=stats )
      call umalloc(maxbl*maxseg*12*2,0,'bcvalj',memuse,stats)
      allocate( bcvalk(maxbl,maxseg,12,2),stat=stats )
      call umalloc(maxbl*maxseg*12*2,0,'bcvalk',memuse,stats)
      allocate( bmat(2*nmds,2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*2*nmds*maxaes,0,'bmat',memuse,stats)
      allocate( cdpt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cdpt',memuse,stats)
      allocate( cdpw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cdpw',memuse,stats)
      allocate( cdt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cdt',memuse,stats)
      allocate( cdvt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cdvt',memuse,stats)
      allocate( cdvw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cdvw',memuse,stats)
      allocate( cdw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cdw',memuse,stats)
      allocate( cfdmom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfdmom',memuse,stats)
      allocate( cfdp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfdp',memuse,stats)
      allocate( cfdtot(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfdtot',memuse,stats)
      allocate( cfdv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfdv',memuse,stats)
      allocate( cflmom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cflmom',memuse,stats)
      allocate( cflp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cflp',memuse,stats)
      allocate( cfltot(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfltot',memuse,stats)
      allocate( cflv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cflv',memuse,stats)
      allocate( cftmom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cftmom',memuse,stats)
      allocate( cftmomw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cftmomw',memuse,stats)
      allocate( cftp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cftp',memuse,stats)
      allocate( cftpw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cftpw',memuse,stats)
      allocate( cfttot(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfttot',memuse,stats)
      allocate( cfttotw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cfttotw',memuse,stats)
      allocate( cftv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cftv',memuse,stats)
      allocate( cftvw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cftvw',memuse,stats)
      allocate( cfxmom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfxmom',memuse,stats)
      allocate( cfxp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfxp',memuse,stats)
      allocate( cfxtot(maxcs),stat=stats )
      call umalloc(maxcs,0,'maxcs',memuse,stats)
      allocate( cfxv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfxv',memuse,stats)
      allocate( cfymom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfymom',memuse,stats)
      allocate( cfyp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfyp',memuse,stats)
      allocate( cfytot(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfytot',memuse,stats)
      allocate( cfyv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfyv',memuse,stats)
      allocate( cfzmom(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfzmom',memuse,stats)
      allocate( cfzp(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfzp',memuse,stats)
      allocate( cfztot(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfztot',memuse,stats)
      allocate( cfzv(maxcs),stat=stats )
      call umalloc(maxcs,0,'cfzv',memuse,stats)
      allocate( chdw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'chdw',memuse,stats)
      allocate( clt(maxbl),stat=stats )
      call umalloc(maxbl,0,'clt',memuse,stats)
      allocate( clw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'clw',memuse,stats)
      allocate( cmxt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cmxt',memuse,stats)
      allocate( cmxw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cmxw',memuse,stats)
      allocate( cmyt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cmyt',memuse,stats)
      allocate( cmyw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cmyw',memuse,stats)
      allocate( cmzt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cmzt',memuse,stats)
      allocate( cmzw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cmzw',memuse,stats)
      allocate( clcd(2,n_clcd,ncycmax),stat=stats )
      call umalloc(2*n_clcd*ncycmax,0,'clcd',memuse,stats)
      clcd(1,:,:) = 1.e21
      allocate( cxt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cxt',memuse,stats)
      allocate( cxw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cxw',memuse,stats)
      allocate( cyt(maxbl),stat=stats )
      call umalloc(maxbl,0,'cyt',memuse,stats)
      allocate( cyw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'cyw',memuse,stats)
      allocate( czt(maxbl),stat=stats )
      call umalloc(maxbl,0,'czt',memuse,stats)
      allocate( czw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'czw',memuse,stats)
      allocate( damp(nmds,maxaes),stat=stats )
      call umalloc(nmds*maxaes,0,'damp',memuse,stats)
      allocate( dthetx(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dthetx',memuse,stats)
      allocate( dthetxx(intmax,nsub1),stat=stats )
      call umalloc(intmax*nsub1,0,'dthetxx',memuse,stats)
      allocate( dthety(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dthety',memuse,stats)
      allocate( dthetyy(intmax,nsub1),stat=stats )
      call umalloc(intmax*nsub1,0,'dthetyy',memuse,stats)
      allocate( dthetz(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dthetz',memuse,stats)
      allocate( dthetzz(intmax,nsub1),stat=stats )
      call umalloc(intmax*nsub1,0,'dthetzz',memuse,stats)
      allocate( dthxmx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dthxmx',memuse,stats)
      allocate( dthymx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dthymx',memuse,stats)
      allocate( dthzmx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dthzmx',memuse,stats)
      allocate( dx(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dx',memuse,stats)
      allocate( dxintg(iitot),stat=stats )
      call umalloc(iitot,0,'dxintg',memuse,stats)
      allocate( dxmx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dxmx',memuse,stats)
      allocate( dy(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dy',memuse,stats)
      allocate( dyintg(iitot),stat=stats )
      call umalloc(iitot,0,'dyintg',memuse,stats)
      allocate( dymx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dymx',memuse,stats)
      allocate( dz(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'dz',memuse,stats)
      allocate( dzintg(iitot),stat=stats )
      call umalloc(iitot,0,'dzintg',memuse,stats)
      allocate( dzmx(maxbl),stat=stats )
      call umalloc(maxbl,0,'dzmx',memuse,stats)
      allocate( eta2s(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'eta2s',memuse,stats)
      allocate( etf1(msub1),stat=stats )
      call umalloc(msub1,1,'etf1',memuse,stats)
      allocate( etf2(msub1),stat=stats )
      call umalloc(msub1,1,'etf2',memuse,stats)
      allocate( factjhi(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'factjhi',memuse,stats)
      allocate( factjlo(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'factjlo',memuse,stats)
      allocate( factkhi(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'factkhi',memuse,stats)
      allocate( factklo(intmx,msub1),stat=stats )
      call umalloc(intmx*msub1,0,'factklo',memuse,stats)
      allocate( fmdot(maxcs),stat=stats )
      call umalloc(maxcs,0,'fmdot',memuse,stats)
      allocate( fmdotw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'fmdotw',memuse,stats)
      allocate( freq(nmds,maxaes),stat=stats )
      call umalloc(nmds*maxaes,0,'freq',memuse,stats)
      allocate( geom_miss(2*mxbli),stat=stats )
      call umalloc(2*mxbli,0,'geom_miss',memuse,stats)
      allocate( gf0(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'gf0',memuse,stats)
      allocate( gforcn(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'gforcn',memuse,stats)
      allocate( gforcnm(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'gforcnm',memuse,stats)
      allocate( gforcs(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'gforcs',memuse,stats)
      allocate( gmass(nmds,maxaes),stat=stats )
      call umalloc(nmds*maxaes,0,'gmass',memuse,stats)
      allocate( iadvance(maxbl),stat=stats )
      call umalloc(maxbl,1,'iadvance',memuse,stats)
      allocate( iaesurf(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'iaesurf',memuse,stats)
      allocate( ibcg(iitot),stat=stats )
      call umalloc(iitot,1,'ibcg',memuse,stats)
      allocate( ibcinfo(maxbl,maxseg,7,2),stat=stats )
      call umalloc(maxbl*maxseg*7*2,1,'ibcinfo',memuse,stats)
      allocate( ibpntsg(maxbl,4),stat=stats )
      call umalloc(maxbl*4,1,'ibpntsg',memuse,stats)
      allocate( icouple(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'icouple',memuse,stats)
      allocate( icsf(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'icsf',memuse,stats)
      allocate( icsi(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'icsi',memuse,stats)
      !allocate( icsinfo(maxcs,9),stat=stats )
      allocate( icsinfo(maxcs,10),stat=stats )!zyf
      icsinfo=0
      !call umalloc(maxcs*9,1,'csinfo',memuse,stats)
      call umalloc(maxcs*10,1,'csinfo',memuse,stats)!zyf
      allocate( idefrm(maxbl),stat=stats )
      call umalloc(maxbl,1,'idefrm',memuse,stats)
      allocate( idegg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'idegg',memuse,stats)
      allocate( idfrmseg(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'idfrmseg',memuse,stats)
      allocate( idiagg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'idiagg',memuse,stats)
      allocate( idimg(maxbl),stat=stats )
      call umalloc(maxbl,1,'idimg',memuse,stats)
      allocate( ieg(maxbl),stat=stats )
      call umalloc(maxbl,1,'ieg',memuse,stats)
      allocate( iemg(maxgr),stat=stats )
      call umalloc(maxgr,1,'iemg',memuse,stats)
      allocate( ifdsg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'ifdsg',memuse,stats)
      allocate( ifiner(intmx),stat=stats )
      call umalloc(intmx,1,'ifiner',memuse,stats)
      allocate( iflimg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'iflimg',memuse,stats)
      allocate( iforce(maxbl),stat=stats )
      call umalloc(maxbl,1,'iforce',memuse,stats)
      allocate( ifrom(msub1),stat=stats )
      call umalloc(msub1,0,'ifrom',memuse,stats)
      allocate( igridg(maxbl),stat=stats )
      call umalloc(maxbl,1,'igridg',memuse,stats)
      allocate( iibg(iitot),stat=stats )
      call umalloc(iitot,1,'iibg',memuse,stats)
      allocate( iic0(intmx),stat=stats )
      call umalloc(intmx,1,'iic0',memuse,stats)
      allocate( iifit(intmx),stat=stats )
      call umalloc(intmx,1,'iifit',memuse,stats)
      allocate( iiig(iitot),stat=stats )
      call umalloc(iitot,1,'iiig',memuse,stats)
      allocate( iiint1(nsub1),stat=stats )
      call umalloc(nsub1,1,'iiint1',memuse,stats)
      allocate( iiint2(nsub1),stat=stats )
      call umalloc(nsub1,1,'iiint2',memuse,stats)
      allocate( iindex(intmax,6*nsub1+9),stat=stats )
      call umalloc(intmax*(6*nsub1+9),1,'iindex',memuse,stats)
      allocate( iindx(intmx,6*msub1+9),stat=stats )
      call umalloc(intmx*(6*msub1+9),1,'iindx',memuse,stats)
      allocate( iiorph(intmx),stat=stats )
      call umalloc(intmx,1,'iiorph',memuse,stats)
      allocate( iipntsg(maxbl),stat=stats )
      call umalloc(maxbl,1,'iipntsg',memuse,stats)
      allocate( iitmax(intmx),stat=stats )
      call umalloc(intmx,1,'iitmax',memuse,stats)
      allocate( iitoss(intmx),stat=stats )
      call umalloc(intmx,1,'iitoss',memuse,stats)
      allocate( ilamhig(maxbl),stat=stats )
      call umalloc(maxbl,1,'ilamhig',memuse,stats)
      allocate( ilamlog(maxbl),stat=stats )
      call umalloc(maxbl,1,'ilamlog',memuse,stats)
      allocate( imx(maxbl),stat=stats )
      call umalloc(maxbl,1,'imx',memuse,stats)
      allocate( inewgg(maxgr),stat=stats )
      call umalloc(maxgr,1,'inewgg',memuse,stats)
      allocate( inpl3d(nplots,11),stat=stats )
      call umalloc(nplots*11,1,'inpl3d',memuse,stats)
      allocate( inpr(nplots,11),stat=stats )
      call umalloc(nplots*11,1,'inpr',memuse,stats)
      allocate( iovrlp(maxbl),stat=stats )
      call umalloc(maxbl,1,'iovrlp',memuse,stats)
      allocate( irotat(maxbl),stat=stats )
      call umalloc(maxbl,1,'irotat',memuse,stats)
      allocate( isav_blk(2*mxbli,17),stat=stats )
      call umalloc(2*mxbli*17,1,'isav_blk',memuse,stats)
      allocate( isav_dpat(intmx,17),stat=stats )
      call umalloc(intmx*17,1,'isav_dpat',memuse,stats)
      allocate( isav_dpat_b(intmx,msub1,6),stat=stats )
      call umalloc(intmx*msub1*6,1,'isav_dpat_b',memuse,stats)
      allocate( isav_emb(lbcemb,12),stat=stats )
      call umalloc(lbcemb*12,1,'isav_emb',memuse,stats)
      allocate( isav_pat(intmax,17),stat=stats )
      call umalloc(intmax*17,1,'isav_pat',memuse,stats)
      allocate( isav_pat_b(intmax,nsub1,6),stat=stats )
      call umalloc(intmax*nsub1*6,1,'isav_pat_b',memuse,stats)
      allocate( isav_prd(lbcprd,12),stat=stats )
      call umalloc(lbcprd*12,1,'isav_prd',memuse,stats)
      allocate( isg(maxbl),stat=stats )
      call umalloc(maxbl,1,'isg',memuse,stats)
      allocate( iskip(maxbl,500),stat=stats )
      call umalloc(500*maxbl,1,'iskip',memuse,stats)
      allocate( istat2_bl(istat_size,mxbli*5),stat=stats )
      call umalloc(istat_size*mxbli*5,1,'istat2_bl',memuse,stats)
      allocate( istat2_em(istat_size,lbcemb*3),stat=stats )
      call umalloc(istat_size*lbcemb*3,1,'istat2_em',memuse,stats)
      allocate( istat2_pa(istat_size,intmax*nsub1*3),stat=stats )
      call umalloc(istat_size*intmax*nsub1*3,1,'istat2_pa',memuse,stats)
      allocate( istat2_pe(istat_size,lbcprd*5),stat=stats )
      call umalloc(istat_size*lbcprd*5,1,'istat2_pe',memuse,stats)
      allocate( isva(2,2,mxbli),stat=stats )
      call umalloc(2*2*mxbli,1,'isva',memuse,stats)
      allocate( itrans(maxbl),stat=stats )
      call umalloc(maxbl,1,'itrans',memuse,stats)
      allocate( iviscg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'iviscg',memuse,stats)
      allocate( ivmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'ivmax',memuse,stats)
      allocate( iwfg(maxbl,3),stat=stats )
      call umalloc(maxbl*3,1,'iwfg',memuse,stats)
      allocate( iwork(mworki),stat=stats )
      call umalloc(mworki,1,'iwork',memuse,stats)
      allocate( jbcinfo(maxbl,maxseg,7,2),stat=stats )
      call umalloc(maxbl*maxseg*7*2,1,'jbcinfo',memuse,stats)
      allocate( jcsf(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'jcsf',memuse,stats)
      allocate( jcsi(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'jcsi',memuse,stats)
      allocate( jdimg(maxbl),stat=stats )
      call umalloc(maxbl,1,'jdimg',memuse,stats)
      allocate( jeg(maxbl),stat=stats )
      call umalloc(maxbl,1,'jeg',memuse,stats)
      allocate( jimage(msub1,mptch+2,mptch+2),stat=stats )
      call umalloc(msub1*(mptch+2)*(mptch+2),1,'jimage',memuse,stats)
      allocate( jjbg(iitot),stat=stats )
      call umalloc(iitot,1,'jjbg',memuse,stats)
      allocate( jjig(iitot),stat=stats )
      call umalloc(iitot,1,'jjig',memuse,stats)
      allocate( jjmax1(nsub1),stat=stats )
      call umalloc(nsub1,1,'jjmax1',memuse,stats)
      allocate( jlamhig(maxbl),stat=stats )
      call umalloc(maxbl,1,'jlamhig',memuse,stats)
      allocate( jlamlog(maxbl),stat=stats )
      call umalloc(maxbl,1,'jlamlog',memuse,stats)
      allocate( jmm(mptch+2),stat=stats )
      call umalloc(mptch+2,1,'jmm',memuse,stats)
      allocate( jmx(maxbl),stat=stats )
      call umalloc(maxbl,1,'jmx',memuse,stats)
      allocate( jsg(maxbl),stat=stats )
      call umalloc(maxbl,1,'jsg',memuse,stats)
      allocate( jskip(maxbl,500),stat=stats )
      call umalloc(500*maxbl,1,'jskip',memuse,stats)
      allocate( jte(msub1),stat=stats )
      call umalloc(msub1,1,'jte',memuse,stats)
      allocate( jvmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'jvmax',memuse,stats)
      allocate( kbcinfo(maxbl,maxseg,7,2),stat=stats )
      call umalloc(maxbl*maxseg*7*2,1,'kbcinfo',memuse,stats)
      allocate( kcsf(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'kcsf',memuse,stats)
      allocate( kcsi(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,1,'kcsi',memuse,stats)
      allocate( kdimg(maxbl),stat=stats )
      call umalloc(maxbl,1,'kdimg',memuse,stats)
      allocate( keg(maxbl),stat=stats )
      call umalloc(maxbl,1,'keg',memuse,stats)
      allocate( kimage(msub1,mptch+2,mptch+2),stat=stats )
      call umalloc(msub1*(mptch+2)*(mptch+2),1,'kimage',memuse,stats)
      allocate( kkbg(iitot),stat=stats )
      call umalloc(iitot,1,'kkbg',memuse,stats)
      allocate( kkig(iitot),stat=stats )
      call umalloc(iitot,1,'kkig',memuse,stats)
      allocate( kkmax1(nsub1),stat=stats )
      call umalloc(nsub1,1,'kkmax1',memuse,stats)
      allocate( klamhig(maxbl),stat=stats )
      call umalloc(maxbl,1,'klamhig',memuse,stats)
      allocate( klamlog(maxbl),stat=stats )
      call umalloc(maxbl,1,'klamlog',memuse,stats)
      allocate( kmm(mptch+2),stat=stats )
      call umalloc(mptch+2,1,'kmm',memuse,stats)
      allocate( kmx(maxbl),stat=stats )
      call umalloc(maxbl,1,'kmx',memuse,stats)
      allocate( ksg(maxbl),stat=stats )
      call umalloc(maxbl,1,'ksg',memuse,stats)
      allocate( kskip(maxbl,500),stat=stats )
      call umalloc(500*maxbl,1,'kskip',memuse,stats)
      allocate( kte(msub1),stat=stats )
      call umalloc(msub1,1,'kte',memuse,stats)
      allocate( kvmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'kvmax',memuse,stats)
      allocate( lbg(maxbl),stat=stats )
      call umalloc(maxbl,1,'lbg',memuse,stats)
      allocate( levelg(maxbl),stat=stats )
      call umalloc(maxbl,1,'levelg',memuse,stats)
      allocate( lig(maxbl),stat=stats )
      call umalloc(maxbl,1,'lig',memuse,stats)
      allocate( limblk(2,6,mxbli),stat=stats )
      call umalloc(2*6*mxbli,1,'limblk',memuse,stats)
      allocate( llimit(intmx),stat=stats )
      call umalloc(intmx,1,'llimit',memuse,stats)
      allocate( lout(msub1),stat=stats )
      call umalloc(msub1,1,'lout',memuse,stats)
      allocate( lw(80,maxbl),stat=stats )
      call umalloc(80*maxbl,1,'lw',memuse,stats)
      allocate( lw2(43,maxbl),stat=stats )
      call umalloc(43*maxbl,1,'lw2',memuse,stats)
      allocate( lwdat(maxbl,maxseg,6),stat=stats )
      call umalloc(maxbl*maxseg*6,1,'lwdat',memuse,stats)
      allocate( mblk2nd(maxbl),stat=stats )
      call umalloc(maxbl,1,'mblk2nd',memuse,stats)
      allocate( mblkpt(mxxe),stat=stats )
      call umalloc(mxxe,1,'mblkpt',memuse,stats)
      allocate( mit(5,maxbl),stat=stats )
      call umalloc(5*maxbl,1,'mit',memuse,stats)
      allocate( mmceta(intmx),stat=stats )
      call umalloc(intmx,1,'mmceta',memuse,stats)
      allocate( mmcxie(intmx),stat=stats )
      call umalloc(intmx,1,'mmcxie',memuse,stats)
      allocate( nbci0(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbci0',memuse,stats)
      allocate( nbcidim(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbcidim',memuse,stats)
      allocate( nbcj0(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbcj0',memuse,stats)
      allocate( nbcjdim(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbcjdim',memuse,stats)
      allocate( nbck0(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbck0',memuse,stats)
      allocate( nbckdim(maxbl),stat=stats )
      call umalloc(maxbl,1,'nbckdim',memuse,stats)
      allocate( nblcg(maxbl),stat=stats )
      call umalloc(maxbl,1,'nblcg',memuse,stats)
      allocate( nblg(maxgr),stat=stats )
      call umalloc(maxgr,1,'nblg',memuse,stats)
      allocate( nblk(2,mxbli),stat=stats )
      call umalloc(2*mxbli,1,'nblk',memuse,stats)
      allocate( nblk1(mptch+2),stat=stats )
      call umalloc(mptch+2,1,'nblk1',memuse,stats)
      allocate( nblk2(mptch+2),stat=stats )
      call umalloc(mptch+2,1,'nblk2',memuse,stats)
      allocate( nblkpt(maxxe),stat=stats )
      call umalloc(maxxe,1,'nblkpt',memuse,stats)
      allocate( nblon(mxbli),stat=stats )
      call umalloc(mxbli,1,'nblon',memuse,stats)
      allocate( ncgg(maxgr),stat=stats )
      call umalloc(maxgr,1,'ncgg',memuse,stats)
      allocate( ncheck(maxbl),stat=stats )
      call umalloc(maxbl,1,'ncheck',memuse,stats)
      allocate( nsegdfrm(maxbl),stat=stats )
      call umalloc(maxbl,1,'nsegdfrm',memuse,stats)
      allocate( omegax(maxbl),stat=stats )
      call umalloc(maxbl,0,'omegax',memuse,stats)
      allocate( omegay(maxbl),stat=stats )
      call umalloc(maxbl,0,'omegay',memuse,stats)
      allocate( omegaz(maxbl),stat=stats )
      call umalloc(maxbl,0,'omegaz',memuse,stats)
      allocate( omgxae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'omgxae',memuse,stats)
      allocate( omgyae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'omgyae',memuse,stats)
      allocate( omgzae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'omgzae',memuse,stats)
      allocate( pav(maxcs),stat=stats )
      call umalloc(maxcs,0,'pav',memuse,stats)
      allocate( period_miss(lbcprd),stat=stats )
      call umalloc(lbcprd,0,'period_miss',memuse,stats)
      allocate( perturb(nmds,maxaes,4),stat=stats )
      call umalloc(nmds*maxaes*4,0,'perturb',memuse,stats)
      allocate( ptav(maxcs),stat=stats )
      call umalloc(maxcs,0,'ptav',memuse,stats)
      allocate( qb(iitot,5,3),stat=stats )
      call umalloc(iitot*5*3,0,'qb',memuse,stats)
      allocate( resmx(maxbl),stat=stats )
      call umalloc(maxbl,0,'resmx',memuse,stats)
      allocate( rfreqr(maxbl),stat=stats )
      call umalloc(maxbl,0,'rfreqr',memuse,stats)
      allocate( rfreqt(maxbl),stat=stats )
      call umalloc(maxbl,0,'rfreqt',memuse,stats)
      allocate( rfrqrae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'rfrqrae',memuse,stats)
      allocate( rfrqtae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'rfrqtae',memuse,stats)
      allocate( rkap0g(maxbl,3),stat=stats )
      call umalloc(maxbl*3,0,'rkap0g',memuse,stats)
      allocate( rms(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'rms',memuse,stats)
      allocate( seta(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'seta',memuse,stats)
      allocate( seta2(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'seta2',memuse,stats)
      allocate( islavept(nslave,nmaster,5),stat=stats )
      call umalloc(nslave*nmaster*5,1,'islavept',memuse,stats)
      allocate( nblelst(maxbl,2),stat=stats )
      call umalloc(maxbl*2,1,'nblelst',memuse,stats)
      allocate( iskmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'iskmax',memuse,stats)
      allocate( jskmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'jskmax',memuse,stats)
      allocate( kskmax(maxbl),stat=stats )
      call umalloc(maxbl,1,'kskmax',memuse,stats)
      allocate( ue(3*nslave),stat=stats )
      call umalloc(3*nslave,0,'ue',memuse,stats)
      allocate( stm(2*nmds,2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*2*nmds*maxaes,0,'stm',memuse,stats)
      allocate( stmi(2*nmds,2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*2*nmds*maxaes,0,'stmi',memuse,stats)
      allocate( stot(maxcs),stat=stats )
      call umalloc(maxcs,0,'stot',memuse,stats)
      allocate( swett(maxbl),stat=stats )
      call umalloc(maxbl,0,'swett',memuse,stats)
      allocate( swetw(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'swetw',memuse,stats)
      allocate( sx(maxcs),stat=stats )
      call umalloc(maxcs,0,'sx',memuse,stats)
      allocate( sxie(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'sxie',memuse,stats)
      allocate( sxie2(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'sxie2',memuse,stats)
      allocate( sy(maxcs),stat=stats )
      call umalloc(maxcs,0,'sy',memuse,stats)
      allocate( sz(maxcs),stat=stats )
      call umalloc(maxcs,0,'sz',memuse,stats)
      allocate( tav(maxcs),stat=stats )
      call umalloc(maxcs,0,'tav',memuse,stats)
      allocate( temp((mptch+2)*(mptch+2)),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'temp',memuse,stats)
      allocate( thetax(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetax',memuse,stats)
      allocate( thetaxl(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetaxl',memuse,stats)
      allocate( thetay(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetay',memuse,stats)
      allocate( thetayl(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetayl',memuse,stats)
      allocate( thetaz(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetaz',memuse,stats)
      allocate( thetazl(maxbl),stat=stats )
      call umalloc(maxbl,0,'thetazl',memuse,stats)
      allocate( thtxae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'thtxae',memuse,stats)
      allocate( thtyae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'thtyae',memuse,stats)
      allocate( thtzae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'thtzae',memuse,stats)
      allocate( time2(maxbl),stat=stats )
      call umalloc(maxbl,0,'time2',memuse,stats)
      allocate( timekeep(ncycmax),stat=stats )
      call umalloc(ncycmax,0,'timekeep',memuse,stats)
      allocate( ttav(maxcs),stat=stats )
      call umalloc(maxcs,0,'ttav',memuse,stats)
      allocate( utrans(maxbl),stat=stats )
      call umalloc(maxbl,0,'utrans',memuse,stats)
      allocate( utrnsae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'utrnsae',memuse,stats)
      allocate( vormax(maxbl),stat=stats )
      call umalloc(maxbl,0,'vormax',memuse,stats)
      allocate( vtrans(maxbl),stat=stats )
      call umalloc(maxbl,0,'vtrans',memuse,stats)
      allocate( vtrnsae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'vtrnsae',memuse,stats)
      allocate( windex(maxxe,2),stat=stats )
      call umalloc(maxxe*2,0,'windex',memuse,stats)
      allocate( windx(mxxe,2),stat=stats )
      call umalloc(mxxe*2,0,'windx',memuse,stats)
      allocate( work(mwork),stat=stats )
      call umalloc(mwork,0,'work',memuse,stats)
      allocate( wtrans(maxbl),stat=stats )
      call umalloc(maxbl,0,'wtrans',memuse,stats)
      allocate( wtrnsae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'wtrnsae',memuse,stats)
      allocate( x0(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'x0',memuse,stats)
      allocate( x1(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'x1',memuse,stats)
      allocate( x2(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'x2',memuse,stats)
      allocate( xie2s(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'xie2s',memuse,stats)
      allocate( xif1(msub1),stat=stats )
      call umalloc(msub1,1,'xif1',memuse,stats)
      allocate( xif2(msub1),stat=stats )
      call umalloc(msub1,1,'xif2',memuse,stats)
      allocate( xmav(maxcs),stat=stats )
      call umalloc(maxcs,0,'xmav',memuse,stats)
      allocate( xmi(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'xmi',memuse,stats)
      allocate( xmie(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'xmie',memuse,stats)
      allocate( xorgae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'xorgae',memuse,stats)
      allocate( xorgae0(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'xorgae0',memuse,stats)
      allocate( xorig(maxbl),stat=stats )
      call umalloc(maxbl,0,'xorig',memuse,stats)
      allocate( xorig0(maxbl),stat=stats )
      call umalloc(maxbl,0,'xorig0',memuse,stats)
      allocate( xs(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'xs',memuse,stats)
      allocate( xte(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'xte',memuse,stats)
      allocate( xxn(2*nmds,maxaes),stat=stats )
      call umalloc(2*nmds*maxaes,0,'xxn',memuse,stats)
      allocate( y1(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'y1',memuse,stats)
      allocate( y2(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'y2',memuse,stats)
      allocate( ymi(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'ymi',memuse,stats)
      allocate( ymie(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'ymie',memuse,stats)
      allocate( yorgae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'yorgae',memuse,stats)
      allocate( yorgae0(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'yorgae0',memuse,stats)
      allocate( yorig(maxbl),stat=stats )
      call umalloc(maxbl,0,'yorig',memuse,stats)
      allocate( yorig0(maxbl),stat=stats )
      call umalloc(maxbl,0,'yorig0',memuse,stats)
      allocate( yte(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'yte',memuse,stats)
      allocate( z1(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'z1',memuse,stats)
      allocate( z2(mptch+2,mptch+2),stat=stats )
      call umalloc((mptch+2)*(mptch+2),0,'z2',memuse,stats)
      allocate( zmi(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'zmi',memuse,stats)
      allocate( zmie(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'zmie',memuse,stats)
      allocate( zorgae(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'zorgae',memuse,stats)
      allocate( zorgae0(maxbl,maxsegdg),stat=stats )
      call umalloc(maxbl*maxsegdg,0,'zorgae0',memuse,stats)
      allocate( zorig(maxbl),stat=stats )
      call umalloc(maxbl,0,'zorig',memuse,stats)
      allocate( zorig0(maxbl),stat=stats )
      call umalloc(maxbl,0,'zorig0',memuse,stats)
      allocate( zte(mptch+2,mptch+2,msub1),stat=stats )
      call umalloc((mptch+2)*(mptch+2)*msub1,0,'zte',memuse,stats)
      allocate( irdrea(maxgr),stat=stats )
      call umalloc(maxgr,1,'irdrea',memuse,stats)
      allocate( iswinfo(maxsw,11),stat=stats )
      call umalloc(maxsw*11,1,'iswinfo',memuse,stats)
      allocate( ismpinfo(maxsmp,11),stat=stats )
      call umalloc(maxsmp*11,1,'ismpinfo',memuse,stats)
c
c***********************************************************************
c     initialize output buffer
c***********************************************************************
c
      do ii=1,nbuf
         nou(ii) = 0
         do mm=1,ibufdim
            bou(mm,ii) = ' '
         end do
      end do
c
c***********************************************************************
c     call flow solver
c***********************************************************************
c
      call mgbl(mwork,mworki,nplots,minnode,iitot,intmax,maxxe,
     .          mxbli,nsub1,lbcprd,lbcemb,lbcrad,maxbl,maxgr,
     .          maxseg,maxcs,ncycmax,intmx,mxxe,mptch,msub1,
     .          ibufdim,nbuf,mxbcfil,istat_size,
     .          work,iwork,lwdat,lw,lw2,nou,bou,bcfilei,
     .          bcfilej,bcfilek,istat2_bl,istat2_pa,istat2_em,
     .          istat2_pe,nblk,limblk,isva,nblon,resmx,imx,
     .          jmx,kmx,vormax,ivmax,jvmax,kvmax,lig,lbg,
     .          iovrlp,qb,ibpntsg,iipntsg,iibg,kkbg,jjbg,ibcg,
     .          dxintg,dyintg,dzintg,iiig,jjig,kkig,rkap0g,
     .          levelg,igridg,iflimg,ifdsg,iviscg,jdimg,
     .          kdimg,idimg,idiagg,nblcg,idegg,jsg,ksg,isg,
     .          jeg,keg,ieg,mit,ilamlog,ilamhig,jlamlog,
     .          jlamhig,klamlog,klamhig,iwfg,utrans,vtrans,
     .          wtrans,omegax,omegay,omegaz,xorig,yorig,
     .          zorig,dxmx,dymx,dzmx,dthxmx,dthymx,dthzmx,
     .          thetax,thetay,thetaz,rfreqt,rfreqr,xorig0,
     .          yorig0,zorig0,time2,thetaxl,thetayl,thetazl,
     .          itrans,irotat,idefrm,bcvali,bcvalj,bcvalk,nbci0,
     .          nbcidim,nbcj0,nbcjdim,nbck0,nbckdim,ibcinfo,
     .          jbcinfo,kbcinfo,ncgg,nblg,iemg,inewgg,
     .          inpl3d,inpr,iadvance,iforce,rms,clw,cdw,
     .          cdpw,cdvw,cxw,cyw,czw,cmxw,cmyw,cmzw,n_clcd,
     .          clcd,nblocks_clcd,blocks_clcd,chdw,
     .          swetw,fmdotw,cfttotw,cftmomw,cftpw,cftvw,
     .          swett,clt,cdt,cxt,
     .          cyt,czt,cmxt,cmyt,cmzt,cdpt,cdvt,sx,sy,sz,
     .          stot,pav,ptav,tav,ttav,xmav,fmdot,cfxp,cfyp,
     .          cfzp,cfdp,cflp,cftp,cfxv,cfyv,cfzv,cfdv,cflv,
     .          cftv,cfxmom,cfymom,cfzmom,cfdmom,cflmom,
     .          cftmom,cfxtot,cfytot,cfztot,cfdtot,cfltot,
     .          cfttot,icsinfo,windex,iindex,nblkpt,windx,
     .          iindx,llimit,iitmax,mmcxie,mmceta,ncheck,
     .          iifit,mblkpt,iic0,iiorph,iitoss,ifiner,
     .          dthetxx,dthetyy,dthetzz,dx,dy,dz,dthetx,
     .          dthety,dthetz,lout,xif1,xif2,etf1,etf2,
     .          jjmax1,kkmax1,iiint1,iiint2,jimage,kimage,
     .          jte,kte,jmm,kmm,nblk1,nblk2,xte,yte,zte,xmi,
     .          ymi,zmi,xmie,ymie,zmie,sxie,seta,sxie2,
     .          seta2,xie2s,eta2s,temp,x2,y2,z2,x1,y1,z1,
     .          factjlo,factjhi,factklo,factkhi,ifrom,
     .          geom_miss,period_miss,isav_blk,isav_prd,
     .          isav_pat,isav_pat_b,isav_dpat,isav_dpat_b,
     .          isav_emb,mblk2nd,ntr,utrnsae,vtrnsae,wtrnsae,
     .          omgxae,omgyae,omgzae,xorgae,yorgae,zorgae,thtxae,
     .          thtyae,thtzae,rfrqtae,rfrqrae,icsi,icsf,jcsi,jcsf,
     .          kcsi,kcsf,freq,gmass,damp,x0,gf0,nmds,maxaes,
     .          aesrfdat,perturb,memuse,bcfiles,islavept,nslave,
     .          iskip,jskip,kskip,bmat,stm,stmi,xs,xxn,gforcn,
     .          gforcnm,gforcs,nsegdfrm,idfrmseg,iaesurf,maxsegdg,
     .          nmaster,aehist,timekeep,xorgae0,yorgae0,zorgae0,
     .          icouple,nblelst,iskmax,jskmax,kskmax,ue,irdrea,
     .          nvdsp,maxsw,iswinfo,maxsmp,ismpinfo)
c
c***********************************************************************
c     deallocate memory
c***********************************************************************
c
c     freeing up the memory at this stage is not really necessary,
c     but it is a VERY good check for code errors that are otherwise
c     hard to detect - if an array has been allocated incorrectly,
c     or the array bounds have been exceeded, then the memory
c     deallocation will fail.
c
c     TEMPORARY FIX: Disable memory deallocation to avoid heap corruption
c     This prevents the "free(): invalid next size" error
      ifree = 0
      if (ifree.gt.0) then
         deallocate(work)
         deallocate(iwork)
         deallocate(lwdat)
         deallocate(lw)
         deallocate(lw2)
         deallocate(nblk)
         deallocate(limblk)
         deallocate(isva)
         deallocate(nblon)
         deallocate(resmx)
         deallocate(imx)
         deallocate(jmx)
         deallocate(kmx)
         deallocate(vormax)
         deallocate(ivmax)
         deallocate(jvmax)
         deallocate(kvmax)
         deallocate(lig)
         deallocate(lbg)
         deallocate(iovrlp)
         deallocate(qb)
         deallocate(ibpntsg)
         deallocate(iipntsg)
         deallocate(iibg)
         deallocate(jjbg)
         deallocate(kkbg)
         deallocate(ibcg)
         deallocate(dxintg)
         deallocate(dyintg)
         deallocate(dzintg)
         deallocate(iiig)
         deallocate(jjig)
         deallocate(kkig)
         deallocate(rkap0g)
         deallocate(levelg)
         deallocate(igridg)
         deallocate(iflimg)
         deallocate(ifdsg)
         deallocate(iviscg)
         deallocate(jdimg)
         deallocate(kdimg)
         deallocate(idimg)
         deallocate(idiagg)
         deallocate(nblcg)
         deallocate(idegg)
         deallocate(jsg)
         deallocate(ksg)
         deallocate(isg)
         deallocate(jeg)
         deallocate(keg)
         deallocate(ieg)
         deallocate(mit)
         deallocate(jlamlog)
         deallocate(klamlog)
         deallocate(ilamlog)
         deallocate(jlamhig)
         deallocate(klamhig)
         deallocate(ilamhig)
         deallocate(iwfg)
         deallocate(utrans)
         deallocate(vtrans)
         deallocate(wtrans)
         deallocate(omegax)
         deallocate(omegay)
         deallocate(omegaz)
         deallocate(xorig)
         deallocate(yorig)
         deallocate(zorig)
         deallocate(dxmx)
         deallocate(dymx)
         deallocate(dzmx)
         deallocate(dthxmx)
         deallocate(dthymx)
         deallocate(dthzmx)
         deallocate(thetax)
         deallocate(thetay)
         deallocate(thetaz)
         deallocate(rfreqt)
         deallocate(rfreqr)
         deallocate(xorig0)
         deallocate(yorig0)
         deallocate(zorig0)
         deallocate(time2)
         deallocate(thetaxl)
         deallocate(thetayl)
         deallocate(thetazl)
         deallocate(itrans)
         deallocate(irotat)
         deallocate(idefrm)
         deallocate(bcvali)
         deallocate(bcvalj)
         deallocate(bcvalk)
         deallocate(nbci0)
         deallocate(nbcj0)
         deallocate(nbck0)
         deallocate(nbcidim)
         deallocate(nbcjdim)
         deallocate(nbckdim)
         deallocate(ibcinfo)
         deallocate(jbcinfo)
         deallocate(kbcinfo)
         deallocate(bcfilei)
         deallocate(bcfilej)
         deallocate(bcfilek)
         deallocate(ncgg)
         deallocate(nblg)
         deallocate(iemg)
         deallocate(inewgg)
         deallocate(inpl3d)
         deallocate(inpr)
         deallocate(iadvance)
         deallocate(iforce)
         deallocate(rms)
         deallocate(clw)
         deallocate(cdw)
         deallocate(cdpw)
         deallocate(cdvw)
         deallocate(cxw)
         deallocate(cyw)
         deallocate(czw)
         deallocate(cmxw)
         deallocate(cmyw)
         deallocate(cmzw)
         deallocate(clcd)
         deallocate(chdw)
         deallocate(swetw)
         deallocate(fmdotw)
         deallocate(cfttotw)
         deallocate(cftmomw)
         deallocate(cftpw)
         deallocate(cftvw)
         deallocate(swett)
         deallocate(clt)
         deallocate(cdt)
         deallocate(cxt)
         deallocate(cyt)
         deallocate(czt)
         deallocate(cmxt)
         deallocate(cmyt)
         deallocate(cmzt)
         deallocate(cdpt)
         deallocate(cdvt)
         deallocate(sx)
         deallocate(sy)
         deallocate(sz)
         deallocate(stot)
         deallocate(pav)
         deallocate(ptav)
         deallocate(tav)
         deallocate(ttav)
         deallocate(xmav)
         deallocate(fmdot)
         deallocate(cfxp)
         deallocate(cfyp)
         deallocate(cfzp)
         deallocate(cfdp)
         deallocate(cflp)
         deallocate(cftp)
         deallocate(cfxv)
         deallocate(cfyv)
         deallocate(cfzv)
         deallocate(cfdv)
         deallocate(cflv)
         deallocate(cftv)
         deallocate(cfxmom)
         deallocate(cfymom)
         deallocate(cfzmom)
         deallocate(cfdmom)
         deallocate(cflmom)
         deallocate(cftmom)
         deallocate(cfxtot)
         deallocate(cfytot)
         deallocate(cfztot)
         deallocate(cfdtot)
         deallocate(cfltot)
         deallocate(cfttot)
         deallocate(icsinfo)
         deallocate(windex)
         deallocate(iindex)
         deallocate(nblkpt)
         deallocate(windx)
         deallocate(iindx)
         deallocate(llimit)
         deallocate(iitmax)
         deallocate(mmcxie)
         deallocate(mmceta)
         deallocate(ncheck)
         deallocate(iifit)
         deallocate(mblkpt)
         deallocate(iic0)
         deallocate(iiorph)
         deallocate(iitoss)
         deallocate(ifiner)
         deallocate(dthetxx)
         deallocate(dthetyy)
         deallocate(dthetzz)
         deallocate(dx)
         deallocate(dy)
         deallocate(dz)
         deallocate(dthetx)
         deallocate(dthety)
         deallocate(dthetz)
         deallocate(lout)
         deallocate(xif1)
         deallocate(xif2)
         deallocate(etf1)
         deallocate(etf2)
         deallocate(jjmax1)
         deallocate(kkmax1)
         deallocate(iiint1)
         deallocate(iiint2)
         deallocate(jimage)
         deallocate(kimage)
         deallocate(jte)
         deallocate(kte)
         deallocate(jmm)
         deallocate(kmm)
         deallocate(nblk1)
         deallocate(nblk2)
         deallocate(xte)
         deallocate(yte)
         deallocate(zte)
         deallocate(xmi)
         deallocate(ymi)
         deallocate(zmi)
         deallocate(xmie)
         deallocate(ymie)
         deallocate(zmie)
         deallocate(sxie)
         deallocate(seta)
         deallocate(sxie2)
         deallocate(seta2)
         deallocate(xie2s)
         deallocate(eta2s)
         deallocate(temp)
         deallocate(x2)
         deallocate(y2)
         deallocate(z2)
         deallocate(x1)
         deallocate(y1)
         deallocate(z1)
         deallocate(factjlo)
         deallocate(factjhi)
         deallocate(factklo)
         deallocate(factkhi)
         deallocate(ifrom)
         deallocate(geom_miss)
         deallocate(period_miss)
         deallocate(isav_blk)
         deallocate(isav_prd)
         deallocate(isav_pat)
         deallocate(isav_pat_b)
         deallocate(isav_dpat)
         deallocate(isav_dpat_b)
         deallocate(isav_emb)
         deallocate(mblk2nd)
         deallocate(istat2_bl)
         deallocate(istat2_pa)
         deallocate(istat2_pe)
         deallocate(istat2_em)
         deallocate(utrnsae)
         deallocate(vtrnsae)
         deallocate(wtrnsae)
         deallocate(omgxae)
         deallocate(omgyae)
         deallocate(omgzae)
         deallocate(xorgae)
         deallocate(yorgae)
         deallocate(zorgae)
         deallocate(xorgae0)
         deallocate(yorgae0)
         deallocate(zorgae0)
         deallocate(icouple)
         deallocate(thtxae)
         deallocate(thtyae)
         deallocate(thtzae)
         deallocate(rfrqtae)
         deallocate(rfrqrae)
         deallocate(icsi)
         deallocate(icsf)
         deallocate(jcsi)
         deallocate(jcsf)
         deallocate(kcsi)
         deallocate(kcsf)
         deallocate(nsegdfrm)
         deallocate(idfrmseg)
         deallocate(iaesurf)
         deallocate(freq)
         deallocate(gmass)
         deallocate(x0)
         deallocate(gf0)
         deallocate(damp)
         deallocate(perturb)
         deallocate(aesrfdat)
         deallocate(islavept)
         deallocate(nblelst)
         deallocate(iskmax)
         deallocate(jskmax)
         deallocate(kskmax)
         deallocate(ue)
         deallocate(iskip)
         deallocate(jskip)
         deallocate(kskip)
         deallocate(bmat)
         deallocate(stm)
         deallocate(stmi)
         deallocate(xs)
         deallocate(xxn)
         deallocate(gforcn)
         deallocate(gforcnm)
         deallocate(gforcs)
         deallocate(aehist)
         deallocate(timekeep)
         deallocate(iswinfo)
         deallocate(ismpinfo)
         if (myid.eq.myhost) then
            write(11,'(/,'' memory for cfl3d has been deallocated'')')
         end if
      end if
c   Write input file used to CGNS file
#if defined CGNS
      if (icgns .eq. 1 .and. myid .eq. myhost) then
        basedesired='Base'
        idimdesired=3
        call wopencgns(grid,basedesired,idimdesired,iccg,
     .              ibase,nzones)
        call writeinput(iccg,ibase,iunit5,irest)
      end if
#endif
c   Close unit 11
      close(11)
#   ifdef FASTIO
      if (myid.eq.myhost) then
c      cfl3d.out.reass is no longer generated. the subroutine reass
c      does not work as expected in some cases, where errors or infinite loop
c      can occur in reass. the subroutine is a disaster to debug due to
c      many "go to", "if", etc., so it is wise to comment it out
c      call reass(maxgr,irdrea)
      end if
#   endif
      deallocate(irdrea)
c
      return
      end
