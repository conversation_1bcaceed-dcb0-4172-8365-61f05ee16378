Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21273
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21273
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 152.59 seconds.
  Return Code: 255
  STDOUT:
           1  of           2  is alive
           0  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 32968 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 32969 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x0000000001cc32e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2b968d791299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc638cf]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x691027]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8bda95]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2b968d732555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f3e000 r-xp 00000000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113d000-0113f000 r--p 00b3d000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113f000-01197000 rw-p 00b3f000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01197000-014ab000 rw-p 00000000 00:00 0 
01bfe000-022ab000 rw-p 00000000 00:00 0                                  [heap]
2b968afd9000-2b968affb000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b968affb000-2b968b005000 rw-p 00000000 00:00 0 
2b968b005000-2b968b006000 rw-s 00000000 00:2f 2595415796                 /dev/shm/Intel_MPI_Zz7F13 (deleted)
2b968b006000-2b968b007000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b968b007000-2b968b008000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b968b008000-2b968b009000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b968b009000-2b968b00a000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b968b00a000-2b968b00c000 rw-p 00000000 00:00 0 
2b968b00c000-2b968b010000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b968b010000-2b968b022000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b968b022000-2b968b025000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b968b025000-2b968b026000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b968b026000-2b968b027000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b968b027000-2b968b033000 rw-p 00000000 00:00 0 
2b968b033000-2b968b034000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b968b034000-2b968b035000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b968b035000-2b968b036000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b968b036000-2b968b037000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b968b037000-2b968b038000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b968b038000-2b968b039000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b968b039000-2b968b03a000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03a000-2b968b03b000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03b000-2b968b03c000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03c000-2b968b03d000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03d000-2b968b03e000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03e000-2b968b03f000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b968b03f000-2b968b040000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b968b040000-2b968b041000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b968b041000-2b968b042000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b968b042000-2b968b043000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b968b043000-2b968b045000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b968b045000-2b968b049000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b968b049000-2b968b04a000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b968b04a000-2b968b04b000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b968b04b000-2b968b04c000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b968b04c000-2b968b04d000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b968b04d000-2b968b04e000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b968b04e000-2b968b04f000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b968b04f000-2b968b050000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b968b050000-2b968b051000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b968b051000-2b968b052000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b968b052000-2b968b053000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b968b053000-2b968b054000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b968b054000-2b968b055000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b968b055000-2b968b056000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b968b056000-2b968b057000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2b968b057000-2b968b058000 rw-p 00000000 00:00 0 
2b968b058000-2b968b059000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2b968b059000-2b968b05c000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b968b05c000-2b968b06b000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b968b06b000-2b968b072000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b968b072000-2b968b073000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b968b073000-2b968b074000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b968b074000-2b968b1ec000 rw-p 00000000 00:00 0 
2b968b1fa000-2b968b1fb000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b968b1fb000-2b968b1fc000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b968b1fc000-2b968b1fd000 rw-p 00000000 00:00 0 
2b968b1fd000-2b968b385000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b968b385000-2b968b585000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b968b585000-2b968b589000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b968b589000-2b968b58d000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b968b58d000-2b968b5b1000 rw-p 00000000 00:00 0 
2b968b5b1000-2b968c43b000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b968c43b000-2b968c63b000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b968c63b000-2b968c64c000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b968c64c000-2b968c65d000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b968c65d000-2b968cde6000 rw-p 00000000 00:00 0 
2b968cde6000-2b968cde8000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b968cde8000-2b968cfe8000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b968cfe8000-2b968cfe9000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b968cfe9000-2b968cfea000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b968cfea000-2b968cff1000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b968cff1000-2b968d1f0000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b968d1f0000-2b968d1f1000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b968d1f1000-2b968d1f2000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b968d1f2000-2b968d209000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b968d209000-2b968d408000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b968d408000-2b968d409000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b968d409000-2b968d40a000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b968d40a000-2b968d40e000 rw-p 00000000 00:00 0 
2b968d40e000-2b968d50f000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b968d50f000-2b968d70e000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b968d70e000-2b968d70f000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b968d70f000-2b968d710000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b968d710000-2b968d805000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d805000-2b968d806000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d806000-2b968d808000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d808000-2b968d809000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d809000-2b968d80f000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d80f000-2b968d811000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d811000-2b968d8d3000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968d8d3000-2b968dad3000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968dad3000-2b968dad7000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968dad7000-2b968dad9000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b968dad9000-2b968dade000 rw-p 00000000 00:00 0 
2b968dade000-2b968dae8000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b968dae8000-2b968dce8000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b968dce8000-2b968dce9000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b968dce9000-2b968dcea000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b968dcea000-2b97314bc000 rw-s 00000000 00:2f 2595415797                 /dev/shm/Intel_MPI_hEKprV (deleted)
2b97314bc000-2b9731504000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b9731504000-2b9731703000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b9731703000-2b9731707000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b9731707000-2b973175e000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b973175e000-2b973195d000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b973195d000-2b9731961000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b9731961000-2b9731978000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b9731978000-2b9731b77000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b9731b77000-2b9731b78000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b9731b78000-2b9731b79000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b9731b79000-2b9731b7a000 rw-p 00000000 00:00 0 
2b9731b7a000-2b9731b94000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b9731b94000-2b9731d93000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b9731d93000-2b9731d94000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b9731d94000-2b9731d95000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b9731d95000-2b9731db3000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b9731db3000-2b9731fb3000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b9731fb3000-2b9731fb5000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b9731fb5000-2b9731fb6000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b9731fb6000-2b973201a000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b973201a000-2b9732219000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b9732219000-2b973221c000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b973221c000-2b9732221000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b9732221000-2b9732223000 rw-p 00000000 00:00 0 
2b9732223000-2b973226b000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b973226b000-2b973246b000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b973246b000-2b973246c000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b973246c000-2b973246d000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b973246d000-2b973246f000 rw-p 00000000 00:00 0 
2b973246f000-2b9732473000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b9732473000-2b9732672000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b9732672000-2b9732673000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b9732673000-2b9732674000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b9732674000-2b973267f000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b973267f000-2b973287e000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b973287e000-2b973287f000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b973287f000-2b9732880000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b9732880000-2b97328c4000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b97328c4000-2b9732ac4000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b9732ac4000-2b9732ac7000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b9732ac7000-2b9732b22000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b9732b22000-2b9732d21000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b9732d21000-2b9732d25000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b9732d25000-2b9732d74000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b9732d74000-2b9732f74000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b9732f74000-2b9732f78000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b9732f78000-2b9732fcb000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b9732fcb000-2b97331cb000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b97331cb000-2b97331cf000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b97331cf000-2b9733302000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b9733302000-2b9733501000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b9733501000-2b9733509000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b9733509000-2b973352f000 rw-p 00000000 00:00 0 
2b973352f000-2b9733570000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b9733570000-2b9733770000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b9733770000-2b9733773000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b9733773000-2b97337cc000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b97337cc000-2b97339cb000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b97339cb000-2b97339cc000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b97339cc000-2b97339cf000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b97339cf000-2b97339f5000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b97339f5000-2b9733bf5000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b9733bf5000-2b9733bf6000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b9733bf6000-2b9733bfa000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b9733bfa000-2b9733d39000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b9733d39000-2b9733f39000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b9733f39000-2b9733f4c000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b9733f4c000-2b9733f53000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b9733f53000-2b9733f5b000 rw-p 00000000 00:00 0 
2b9733f5b000-2b9733f6d000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b9733f6d000-2b973416c000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b973416c000-2b973416d000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b973416d000-2b973416e000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b973416e000-2b973416f000 rw-p 00000000 00:00 0 
2b973416f000-2b97341ca000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b97341ca000-2b97343ca000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b97343ca000-2b97343cb000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b97343cb000-2b97343d0000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b97343d0000-2b97343db000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b97343db000-2b97345da000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b97345da000-2b97345db000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b97345db000-2b97345dc000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b97345dc000-2b97345df000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b97345df000-2b97347df000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b97347df000-2b97347e0000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b97347e0000-2b97347e1000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b97347e1000-2b97347e5000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b97347e5000-2b97349e4000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b97349e4000-2b97349e5000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b97349e5000-2b97349e6000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b97349e6000-2b9734a6b000 rw-p 00000000 00:00 0 
2b9734a6b000-2b9734a6c000 ---p 00000000 00:00 0 
2b9734a6c000-2b9734e00000 rw-p 00000000 00:00 0 
2b9734e00000-2b9735400000 rw-p 00000000 00:00 0 
2b9735400000-2b9735600000 rw-p 00000000 00:00 0 
2b9735600000-2b9737c00000 rw-p 00000000 00:00 0 
2b9737c00000-2b9737e00000 rw-p 00000000 00:00 0 
2b9737e00000-2b9739200000 rw-p 00000000 00:00 0 
2b9739200000-2b973f800000 rw-p 00000000 00:00 0 
2b973f800000-2b9740200000 rw-p 00000000 00:00 0 
2b9740200000-2b9740210000 rw-p 00000000 00:00 0 
2b9744000000-2b9744021000 rw-p 00000000 00:00 0 
2b9744021000-2b9748000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003200000 rw-p 00000000 00:00 0 
7ffd41916000-7ffd4193f000 rw-p 00000000 00:00 0                          [stack]
7ffd4194f000-7ffd41951000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
