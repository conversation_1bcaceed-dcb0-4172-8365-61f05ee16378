#     $Id$
#=============================================================================
#
#              creates common libraries for subroutines common to 
#              both the sequential and parallel versions of cfl3d
#              note that this excludes any file that contains 
#              MPI/sequential protocols activated by "if def"
#
#=============================================================================

# ****************************** CREATE LINKS ********************************

link: lncode lnhead

lncode:
	@ echo "        linking source code"
	ln -s $(CFLSRC_S)/*.F .
	ln -s $(CFLSRC_S)/*.c .

lnhead:

# ***************************** SUFFIX RULES***********************************

#                    (to convert .F files to .f files)

.SUFFIXES: .F .TMP1

.F.f:
	$(CPP) $(CPPFLAG) $(CPPOPT) $*.F > $*.TMP1
	@grep -v '^[ ]*$$' < $*.TMP1 > $*.f
	@chmod 600 $*.f
	@rm $*.TMP1

# *************************** LIBRARY DEFINITIONS ****************************

FSRC_LIBS = \
	add2x.f        resadd.f       blocki_d.f     blockj_d.f \
	init_mast.f    blockk_d.f     mreal.f        barth3d.f \
	outbuf.f       get_bvals.f    qface.f        xmukin.f \
	rotateq2_d.f   abciz.f        diagk.f        wkstn.f \
	abcjz.f        colldat.f      dlutr.f        abckz.f \
	collv.f        dlutrp.f       init.f         q8vrev.f \
	collx.f        dsmin.f        initvist.f     lamfix.f \
	collxtb.f      expand.f       int2.f         bc9999.f \
	extra.f        extrae.f       lead.f \
	amafi.f        ffluxl.f       l2norm.f       bc_xmera.f \
	amafj.f        dabciz.f       fhat.f         l2norm2.f \
	amafk.f        dabcjz.f       fill.f         blkmax.f \
	avgint.f       dabckz.f       loadgr.f       newfit.f \
	blocki.f       dfbtr.f        getdhdr.f      ld_qc.f \
	blockj.f       dfbtrp.f       gfluxl.f       project.f \
	blockk.f       dfhat.f        grdmove.f      q8sdot.f \
	bsub.f         dfluxpm.f      hfluxl.f       q8smax.f \
	bsubp.f        diagi.f        q8smin.f       spalart.f \
	ccf.f          diagj.f        hole.f         global0.f \
	tau2x.f        tdq.f          tinvr.f        tmetric.f \
	trace.f        trans.f        transmc.f      transp.f \
	vlutrp.f       xe.f           xlim.f         unld_qc.f \
	xtbatb.f       rie1d.f        rie1de.f       rotateq.f \
	rotateq0.f     rotatmc.f      rotatp.f       twoeqn.f \
	rp3d.f         setblk.f       setdqc0.f      triv.f \
        setqc0.f       swafi.f        swafj.f        swafk.f \
	af3f.f         resid.f        update.f       cellvol.f \
	chkrap.f       chksym.f       gfluxv.f       hfluxv.f \
	ffluxv.f       bcchk.f        blomax.f       dird.f \
	direct.f       ctime1.f       dthole.f       diagnos.f \
	fa2xi.f        fa2xj.f        fa2xk.f        ffluxr.f \
	gfluxr.f       hfluxr.f       fluxm.f        fluxp.f \
	force.f        coll2q.f       collq.f        collqc0.f \
	collapse.f     collxt.f       rotate.f       rsmooth.f \
	shear.f        topol2.f       xe2.f          vlutr.f \
	rechk.f        metric.f       invert.f       ld_datj.f \
	ld_datk.f      ld_dati.f      topol.f        arc.f \
        wmag.f         chkrot.f       chkrotj_d.f    chkrotk_d.f \
	chkroti_d.f    int2_d.f       i2x.f          i2xj_d.f \
	i2xk_d.f       i2xi_d.f       i2xs.f         i2xsj_d.f \
	i2xsk_d.f      i2xsi_d.f      readdat.f      cblki.f \
	cblkj.f        cblkk.f        cblki_d.f      cblkj_d.f \
	cblkk_d.f      bc2005.f       bc2005j_d.f    bc2005k_d.f \
	bc2005i_d.f    pre_blockbc.f  pre_blocki.f   pre_blockj.f \
	pre_blockk.f   bc_info.f      bc1000.f       bc1001.f \
	bc1002.f       bc1003.f       bc1005.f       bc1008.f \
	bc1011.f       bc1012.f       bc1013.f       bc2002.f \
	bc2003.f       bc2004.f       bc2006.f       bc2007.f \
	bc2008.f       bc2102.f       rotateqb.f     rpatch.f \
	delv.f         xupdt.f        intrbc.f       fa.f \
	tau.f          histout.f      csurf.f        csout.f \
	mvdat.f        getibk.f       pre_embed.f    pre_period.f \
	pre_patch.f    pre_cblki.f    pre_cblkj.f    pre_cblkk.f \
	global.f       global2.f      bc.f           getibk0.f \
	rpatch0.f      cntsurf.f      cctogp.f       bc2009.f \
	modread.f      genforce.f     rotsurf.f      mms.f \
	trnsurf.f      tfiedge.f      tfiface.f      tfivol.f  \
        arclen.f       deform.f       bc_delt.f      getsurf.f \
	setcorner.f    prolim.f       prolim2.f      readkey.f \
	parser.f       ae_pred.f      collmod.f      init_ae.f \
	aesurf.f       chkdef.f       moddefl.f      xyzintr.f \
        delintr.f      rcfl.f         ccomplex.f     setseg.f \
	cgnstools.f    fmaps.f        getdelt.f      rsurf.f \
	avghole.f      augmntq.f      blnkfr.f       gradinfo.f \
	rb_pred.f      rb_corr.f      init_rb.f      init_trim.f \
	pltmode.f      bcnonin.f      initnonin.f    resnonin.f \
        my_flush.f     bc2016.f       gfluxv1.f      hfluxv1.f \
        ffluxv1.f      sijrate2d.f    threeeqn.f     lesdiag.f \
        sijrate3d.f    foureqn.f      bc2026.f       bc2019.f \
        u_doubleprime.f bc2010.f

FSRC_SPEC = addx.f

CSRC_LIBS = bessel.c

FOBJ_LIBS = $(FSRC_LIBS:.f=.o)

FOBJ_SPEC = $(FSRC_SPEC:.f=.o)

COBJ_LIBS = $(CSRC_LIBS:.c=.o)

COMMONLIB = libcommon.a

$(COMMONLIB): $(FSRC_LIBS) $(FOBJ_LIBS) $(FSRC_SPEC) $(FOBJ_SPEC) \
	$(CSRC_LIBS) $(COBJ_LIBS)
	ar $(AROPT) $(COMMONLIB) $(FOBJ_LIBS) $(FOBJ_SPEC) $(COBJ_LIBS) 
	@$(RANLIB) $(COMMONLIB)

$(FOBJ_LIBS): 
	$(FTN) $(FFLAG) -c $*.f

$(FOBJ_SPEC):
	$(FTN) $(FFLAG_SPEC) -c $*.f

$(COBJ_LIBS):
	$(CC) $(CFLAG) -c $*.c

$(EXEC): $(COMMONLIB)
	@ echo "                                                              "
	@ echo "=============================================================="
	@ echo "                                                              "
	@ echo "               DONE:  $(COMMONLIB) created                    "
	@ echo "                                                              "
	@ echo "           the archive libraries can be found in:             "
	@ echo "                                                              "
	@ echo "                   $(DIR)/$(COMMONLIB)                        "
	@ echo "                                                              "
	@ echo "=============================================================="
	@ echo "                                                              "

# ****************************** CLEAN/SCRUB *********************************

# the @touch is used to (silently) create some temp files to prevent irksome
# warning messages are sometimes created if there are no *.whatever files and
# one tries to remove them

cleano:
	@touch temp.o
	-rm -f *.o

cleane:
	-rm -f $(EXEC) 

cleana:
	@touch temp.a
	-rm -f *.a

cleanf:
	@touch temp.f
	-rm -f *.f

cleanh:
	@touch temp.h
	-rm -f *.h

cleang:
	@touch temp.F
	-rm -f *.F

cleanc:
	@touch temp.c
	-rm -f *.c

scrub: cleana cleano cleane cleanf cleanh cleang cleanc
