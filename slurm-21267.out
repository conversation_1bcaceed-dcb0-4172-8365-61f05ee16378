Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21267
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21267
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 152.67 seconds.
  Return Code: 255
  STDOUT:
           0  of           2  is alive
           1  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 27153 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 27154 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x00000000017792e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2b348ac0c299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc7304f]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x68d34b]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8cd215]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2b348abad555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f4e000 r-xp 00000000 00:29 495336090                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0114d000-0114f000 r--p 00b4d000 00:29 495336090                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0114f000-011a7000 rw-p 00b4f000 00:29 495336090                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
011a7000-014bb000 rw-p 00000000 00:00 0 
016b4000-01d61000 rw-p 00000000 00:00 0                                  [heap]
2b3488454000-2b3488476000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b3488476000-2b3488480000 rw-p 00000000 00:00 0 
2b3488480000-2b3488481000 rw-s 00000000 00:2e 2595314664                 /dev/shm/Intel_MPI_QveMoB (deleted)
2b3488481000-2b3488482000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b3488482000-2b3488483000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b3488483000-2b3488484000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b3488484000-2b3488485000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b3488485000-2b3488487000 rw-p 00000000 00:00 0 
2b3488487000-2b348848b000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b348848b000-2b348849d000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b348849d000-2b34884a0000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b34884a0000-2b34884a1000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b34884a1000-2b34884a2000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b34884a2000-2b34884ae000 rw-p 00000000 00:00 0 
2b34884ae000-2b34884af000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b34884af000-2b34884b0000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b0000-2b34884b1000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b1000-2b34884b2000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b2000-2b34884b3000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b3000-2b34884b4000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b4000-2b34884b5000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b5000-2b34884b6000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b6000-2b34884b7000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b7000-2b34884b8000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b8000-2b34884b9000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b34884b9000-2b34884ba000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b34884ba000-2b34884bb000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b34884bb000-2b34884bc000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b34884bc000-2b34884bd000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b34884bd000-2b34884be000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b34884be000-2b34884c0000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b34884c0000-2b34884c4000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b34884c4000-2b34884c5000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b34884c5000-2b34884c6000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b34884c6000-2b34884c7000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b34884c7000-2b34884c8000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b34884c8000-2b34884c9000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b34884c9000-2b34884ca000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b34884ca000-2b34884cb000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b34884cb000-2b34884cc000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b34884cc000-2b34884cd000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b34884cd000-2b34884ce000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b34884ce000-2b34884cf000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b34884cf000-2b34884d0000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b34884d0000-2b34884d1000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b34884d1000-2b34884d2000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2b34884d2000-2b34884d3000 rw-p 00000000 00:00 0 
2b34884d3000-2b34884d4000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2b34884d4000-2b34884d7000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b34884d7000-2b34884e6000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b34884e6000-2b34884ed000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b34884ed000-2b34884ee000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b34884ee000-2b34884ef000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b34884ef000-2b3488667000 rw-p 00000000 00:00 0 
2b3488675000-2b3488676000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b3488676000-2b3488677000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b3488677000-2b3488678000 rw-p 00000000 00:00 0 
2b3488678000-2b3488800000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b3488800000-2b3488a00000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b3488a00000-2b3488a04000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b3488a04000-2b3488a08000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b3488a08000-2b3488a2c000 rw-p 00000000 00:00 0 
2b3488a2c000-2b34898b6000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b34898b6000-2b3489ab6000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b3489ab6000-2b3489ac7000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b3489ac7000-2b3489ad8000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b3489ad8000-2b348a261000 rw-p 00000000 00:00 0 
2b348a261000-2b348a263000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b348a263000-2b348a463000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b348a463000-2b348a464000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b348a464000-2b348a465000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b348a465000-2b348a46c000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b348a46c000-2b348a66b000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b348a66b000-2b348a66c000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b348a66c000-2b348a66d000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b348a66d000-2b348a684000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b348a684000-2b348a883000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b348a883000-2b348a884000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b348a884000-2b348a885000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b348a885000-2b348a889000 rw-p 00000000 00:00 0 
2b348a889000-2b348a98a000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b348a98a000-2b348ab89000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b348ab89000-2b348ab8a000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b348ab8a000-2b348ab8b000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b348ab8b000-2b348ac80000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac80000-2b348ac81000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac81000-2b348ac83000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac83000-2b348ac84000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac84000-2b348ac8a000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac8a000-2b348ac8c000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ac8c000-2b348ad4e000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348ad4e000-2b348af4e000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348af4e000-2b348af52000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348af52000-2b348af54000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b348af54000-2b348af59000 rw-p 00000000 00:00 0 
2b348af59000-2b348af63000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b348af63000-2b348b163000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b348b163000-2b348b164000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b348b164000-2b348b165000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b348b165000-2b352e937000 rw-s 00000000 00:2e 2595314665                 /dev/shm/Intel_MPI_7GQU81 (deleted)
2b352e937000-2b352e97f000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b352e97f000-2b352eb7e000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b352eb7e000-2b352eb82000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b352eb82000-2b352ebd9000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b352ebd9000-2b352edd8000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b352edd8000-2b352eddc000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b352eddc000-2b352edf3000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b352edf3000-2b352eff2000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b352eff2000-2b352eff3000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b352eff3000-2b352eff4000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b352eff4000-2b352eff5000 rw-p 00000000 00:00 0 
2b352eff5000-2b352f00f000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b352f00f000-2b352f20e000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b352f20e000-2b352f20f000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b352f20f000-2b352f210000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b352f210000-2b352f22e000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b352f22e000-2b352f42e000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b352f42e000-2b352f430000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b352f430000-2b352f431000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b352f431000-2b352f495000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b352f495000-2b352f694000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b352f694000-2b352f697000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b352f697000-2b352f69c000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b352f69c000-2b352f69e000 rw-p 00000000 00:00 0 
2b352f69e000-2b352f6e6000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b352f6e6000-2b352f8e6000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b352f8e6000-2b352f8e7000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b352f8e7000-2b352f8e8000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b352f8e8000-2b352f8ea000 rw-p 00000000 00:00 0 
2b352f8ea000-2b352f8ee000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b352f8ee000-2b352faed000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b352faed000-2b352faee000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b352faee000-2b352faef000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b352faef000-2b352fafa000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b352fafa000-2b352fcf9000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b352fcf9000-2b352fcfa000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b352fcfa000-2b352fcfb000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b352fcfb000-2b352fd3f000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b352fd3f000-2b352ff3f000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b352ff3f000-2b352ff42000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b352ff42000-2b352ff9d000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b352ff9d000-2b353019c000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b353019c000-2b35301a0000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b35301a0000-2b35301ef000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b35301ef000-2b35303ef000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b35303ef000-2b35303f3000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b35303f3000-2b3530446000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b3530446000-2b3530646000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b3530646000-2b353064a000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b353064a000-2b353077d000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b353077d000-2b353097c000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b353097c000-2b3530984000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b3530984000-2b35309aa000 rw-p 00000000 00:00 0 
2b35309aa000-2b35309eb000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b35309eb000-2b3530beb000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b3530beb000-2b3530bee000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b3530bee000-2b3530c47000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b3530c47000-2b3530e46000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b3530e46000-2b3530e47000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b3530e47000-2b3530e4a000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b3530e4a000-2b3530e70000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b3530e70000-2b3531070000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b3531070000-2b3531071000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b3531071000-2b3531075000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b3531075000-2b35311b4000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b35311b4000-2b35313b4000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b35313b4000-2b35313c7000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b35313c7000-2b35313ce000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b35313ce000-2b35313d6000 rw-p 00000000 00:00 0 
2b35313d6000-2b35313e8000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b35313e8000-2b35315e7000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b35315e7000-2b35315e8000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b35315e8000-2b35315e9000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b35315e9000-2b35315ea000 rw-p 00000000 00:00 0 
2b35315ea000-2b3531645000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b3531645000-2b3531845000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b3531845000-2b3531846000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b3531846000-2b353184b000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b353184b000-2b3531856000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b3531856000-2b3531a55000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b3531a55000-2b3531a56000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b3531a56000-2b3531a57000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b3531a57000-2b3531a5a000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b3531a5a000-2b3531c5a000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b3531c5a000-2b3531c5b000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b3531c5b000-2b3531c5c000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b3531c5c000-2b3531c60000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b3531c60000-2b3531e5f000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b3531e5f000-2b3531e60000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b3531e60000-2b3531e61000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b3531e61000-2b3531ee6000 rw-p 00000000 00:00 0 
2b3531ee6000-2b3531ee7000 ---p 00000000 00:00 0 
2b3531ee7000-2b3532200000 rw-p 00000000 00:00 0 
2b3532200000-2b3532800000 rw-p 00000000 00:00 0 
2b3532800000-2b3532a00000 rw-p 00000000 00:00 0 
2b3532a00000-2b3535000000 rw-p 00000000 00:00 0 
2b3535000000-2b3535200000 rw-p 00000000 00:00 0 
2b3535200000-2b3536600000 rw-p 00000000 00:00 0 
2b3536600000-2b353676f000 rw-p 00000000 00:00 0 
2b353ca8a000-2b353cc00000 rw-p 00000000 00:00 0 
2b353cc00000-2b353d600000 rw-p 00000000 00:00 0 
2b353d600000-2b353d68b000 rw-p 00000000 00:00 0 
2b3540000000-2b3540021000 rw-p 00000000 00:00 0 
2b3540021000-2b3544000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003200000 rw-p 00000000 00:00 0 
7ffdb3281000-7ffdb32a9000 rw-p 00000000 00:00 0                          [stack]
7ffdb333c000-7ffdb333e000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
