Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 20816
GPUs allocated: 0,1,2,3,4,5,6,7
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 20816
SLURM Node List: g0005
Running on host: g0005
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 190.12 seconds.
  Return Code: 255
  STDOUT:
           0  of           2  is alive
           1  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 22241 RUNNING AT g0005
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 22242 RUNNING AT g0005
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x000000000278f2e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2af67546d299]
../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc7305f]
../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x68d35b]
../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8cd225]
../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406922]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2af67540e555]
../../../zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f4e000 r-xp 00000000 00:27 467963971                          /home/<USER>/gpuuser255/zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0114d000-0114f000 r--p 00b4d000 00:27 467963971                          /home/<USER>/gpuuser255/zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0114f000-011a7000 rw-p 00b4f000 00:27 467963971                          /home/<USER>/gpuuser255/zzy/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
011a7000-014bb000 rw-p 00000000 00:00 0 
026ca000-02d77000 rw-p 00000000 00:00 0                                  [heap]
2af672cb5000-2af672cd7000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2af672cd7000-2af672ce1000 rw-p 00000000 00:00 0 
2af672ce1000-2af672ce2000 rw-s 00000000 00:2b 2145394114                 /dev/shm/Intel_MPI_HouFDZ (deleted)
2af672ce2000-2af672ce3000 -w-s 00000000 00:05 33866                      /dev/infiniband/uverbs0
2af672ce3000-2af672ce4000 -w-s 00001000 00:05 33866                      /dev/infiniband/uverbs0
2af672ce4000-2af672ce5000 -w-s 00002000 00:05 33866                      /dev/infiniband/uverbs0
2af672ce5000-2af672ce6000 -w-s 00003000 00:05 33866                      /dev/infiniband/uverbs0
2af672ce6000-2af672ce8000 rw-p 00000000 00:00 0 
2af672ce8000-2af672cec000 r--p 00000000 00:27 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2af672cec000-2af672cfe000 r-xp 00004000 00:27 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2af672cfe000-2af672d01000 r--p 00016000 00:27 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2af672d01000-2af672d02000 r--p 00019000 00:27 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2af672d02000-2af672d03000 rw-p 0001a000 00:27 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2af672d03000-2af672d0f000 rw-p 00000000 00:00 0 
2af672d0f000-2af672d10000 -w-s 00004000 00:05 33866                      /dev/infiniband/uverbs0
2af672d10000-2af672d11000 -w-s 00005000 00:05 33866                      /dev/infiniband/uverbs0
2af672d11000-2af672d12000 -w-s 00006000 00:05 33866                      /dev/infiniband/uverbs0
2af672d12000-2af672d13000 -w-s 00007000 00:05 33866                      /dev/infiniband/uverbs0
2af672d13000-2af672d14000 r--s 00500000 00:05 33866                      /dev/infiniband/uverbs0
2af672d14000-2af672d15000 r--s 00700000 00:05 33866                      /dev/infiniband/uverbs0
2af672d15000-2af672d16000 -w-s 00000000 00:05 33866                      /dev/infiniband/uverbs0
2af672d16000-2af672d17000 -w-s 00001000 00:05 33866                      /dev/infiniband/uverbs0
2af672d17000-2af672d18000 -w-s 00002000 00:05 33866                      /dev/infiniband/uverbs0
2af672d18000-2af672d19000 -w-s 00003000 00:05 33866                      /dev/infiniband/uverbs0
2af672d19000-2af672d1a000 -w-s 00004000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1a000-2af672d1b000 -w-s 00005000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1b000-2af672d1c000 -w-s 00006000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1c000-2af672d1d000 -w-s 00007000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1d000-2af672d1e000 r--s 00500000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1e000-2af672d1f000 r--s 00700000 00:05 33866                      /dev/infiniband/uverbs0
2af672d1f000-2af672d21000 r--p 00000000 00:27 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2af672d21000-2af672d25000 r-xp 00002000 00:27 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2af672d25000-2af672d26000 r--p 00006000 00:27 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2af672d26000-2af672d27000 r--p 00006000 00:27 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2af672d27000-2af672d28000 rw-p 00007000 00:27 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2af672d28000-2af672d29000 -w-s 00000000 00:05 33866                      /dev/infiniband/uverbs0
2af672d29000-2af672d2a000 -w-s 00001000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2a000-2af672d2b000 -w-s 00002000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2b000-2af672d2c000 -w-s 00003000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2c000-2af672d2d000 -w-s 00004000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2d000-2af672d2e000 -w-s 00005000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2e000-2af672d2f000 -w-s 00006000 00:05 33866                      /dev/infiniband/uverbs0
2af672d2f000-2af672d30000 -w-s 00007000 00:05 33866                      /dev/infiniband/uverbs0
2af672d30000-2af672d31000 r--s 00500000 00:05 33866                      /dev/infiniband/uverbs0
2af672d31000-2af672d32000 r--s 00700000 00:05 33866                      /dev/infiniband/uverbs0
2af672d32000-2af672d33000 rw-s 00800000 00:05 33866                      /dev/infiniband/uverbs0
2af672d33000-2af672d34000 rw-p 00000000 00:00 0 
2af672d34000-2af672d35000 -w-s 00600000 00:05 33866                      /dev/infiniband/uverbs0
2af672d35000-2af672d38000 r--p 00000000 00:27 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2af672d38000-2af672d47000 r-xp 00003000 00:27 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2af672d47000-2af672d4e000 r--p 00012000 00:27 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2af672d4e000-2af672d4f000 r--p 00018000 00:27 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2af672d4f000-2af672d50000 rw-p 00019000 00:27 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2af672d50000-2af672ec8000 rw-p 00000000 00:00 0 
2af672ed6000-2af672ed7000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2af672ed7000-2af672ed8000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2af672ed8000-2af672ed9000 rw-p 00000000 00:00 0 
2af672ed9000-2af673061000 r-xp 00000000 00:27 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2af673061000-2af673261000 ---p 00188000 00:27 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2af673261000-2af673265000 r--p 00188000 00:27 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2af673265000-2af673269000 rw-p 0018c000 00:27 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2af673269000-2af67328d000 rw-p 00000000 00:00 0 
2af67328d000-2af674117000 r-xp 00000000 00:27 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2af674117000-2af674317000 ---p 00e8a000 00:27 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2af674317000-2af674328000 r--p 00e8a000 00:27 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2af674328000-2af674339000 rw-p 00e9b000 00:27 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2af674339000-2af674ac2000 rw-p 00000000 00:00 0 
2af674ac2000-2af674ac4000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2af674ac4000-2af674cc4000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2af674cc4000-2af674cc5000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2af674cc5000-2af674cc6000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2af674cc6000-2af674ccd000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2af674ccd000-2af674ecc000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2af674ecc000-2af674ecd000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2af674ecd000-2af674ece000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2af674ece000-2af674ee5000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2af674ee5000-2af6750e4000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2af6750e4000-2af6750e5000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2af6750e5000-2af6750e6000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2af6750e6000-2af6750ea000 rw-p 00000000 00:00 0 
2af6750ea000-2af6751eb000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2af6751eb000-2af6753ea000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2af6753ea000-2af6753eb000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2af6753eb000-2af6753ec000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2af6753ec000-2af6754e1000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754e1000-2af6754e2000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754e2000-2af6754e4000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754e4000-2af6754e5000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754e5000-2af6754eb000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754eb000-2af6754ed000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6754ed000-2af6755af000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6755af000-2af6757af000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6757af000-2af6757b3000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6757b3000-2af6757b5000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2af6757b5000-2af6757ba000 rw-p 00000000 00:00 0 
2af6757ba000-2af6757c4000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2af6757c4000-2af6759c4000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2af6759c4000-2af6759c5000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2af6759c5000-2af6759c6000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2af6759c6000-2af675bc6000 rw-s 00000000 00:2b 2145394115                 /dev/shm/Intel_MPI_I33uHG (deleted)
2af675bc6000-2af6b5bc6000 rw-s 00200000 00:2b 2145394115                 /dev/shm/Intel_MPI_I33uHG (deleted)
2af6b5bc6000-2af761198000 rw-s 40200000 00:2b 2145394115                 /dev/shm/Intel_MPI_I33uHG (deleted)
2af761198000-2af7611e0000 r-xp 00000000 00:27 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2af7611e0000-2af7613df000 ---p 00048000 00:27 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2af7613df000-2af7613e3000 rw-p 00047000 00:27 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2af7613e3000-2af76143a000 r-xp 00000000 00:27 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2af76143a000-2af761639000 ---p 00057000 00:27 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2af761639000-2af76163d000 rw-p 00056000 00:27 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2af76163d000-2af761654000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2af761654000-2af761853000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2af761853000-2af761854000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2af761854000-2af761855000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2af761855000-2af761856000 rw-p 00000000 00:00 0 
2af761856000-2af761870000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2af761870000-2af761a6f000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2af761a6f000-2af761a70000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2af761a70000-2af761a71000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2af761a71000-2af761a8f000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2af761a8f000-2af761c8f000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2af761c8f000-2af761c91000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2af761c91000-2af761c92000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2af761c92000-2af761cf6000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2af761cf6000-2af761ef5000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2af761ef5000-2af761ef8000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2af761ef8000-2af761efd000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2af761efd000-2af761eff000 rw-p 00000000 00:00 0 
2af761eff000-2af761f47000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2af761f47000-2af762147000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2af762147000-2af762148000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2af762148000-2af762149000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2af762149000-2af76214b000 rw-p 00000000 00:00 0 
2af76214b000-2af76214f000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2af76214f000-2af76234e000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2af76234e000-2af76234f000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2af76234f000-2af762350000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2af762350000-2af76235b000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2af76235b000-2af76255a000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2af76255a000-2af76255b000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2af76255b000-2af76255c000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2af76255c000-2af7625a0000 r-xp 00000000 00:27 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2af7625a0000-2af7627a0000 ---p 00044000 00:27 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2af7627a0000-2af7627a3000 rw-p 00044000 00:27 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2af7627a3000-2af7627fe000 r-xp 00000000 00:27 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2af7627fe000-2af7629fd000 ---p 0005b000 00:27 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2af7629fd000-2af762a01000 rw-p 0005a000 00:27 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2af762a01000-2af762a50000 r-xp 00000000 00:27 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2af762a50000-2af762c50000 ---p 0004f000 00:27 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2af762c50000-2af762c54000 rw-p 0004f000 00:27 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2af762c54000-2af762ca7000 r-xp 00000000 00:27 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2af762ca7000-2af762ea7000 ---p 00053000 00:27 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2af762ea7000-2af762eab000 rw-p 00053000 00:27 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2af762eab000-2af762fde000 r-xp 00000000 00:27 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2af762fde000-2af7631dd000 ---p 00133000 00:27 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2af7631dd000-2af7631e5000 rw-p 00132000 00:27 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2af7631e5000-2af76320b000 rw-p 00000000 00:00 0 
2af76320b000-2af76324c000 r-xp 00000000 00:27 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2af76324c000-2af76344c000 ---p 00041000 00:27 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2af76344c000-2af76344f000 rw-p 00041000 00:27 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2af76344f000-2af7634a8000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2af7634a8000-2af7636a7000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2af7636a7000-2af7636a8000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2af7636a8000-2af7636ab000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2af7636ab000-2af7636d1000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2af7636d1000-2af7638d1000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2af7638d1000-2af7638d2000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2af7638d2000-2af7638d6000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2af7638d6000-2af763a15000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2af763a15000-2af763c15000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2af763c15000-2af763c28000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2af763c28000-2af763c2f000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2af763c2f000-2af763c37000 rw-p 00000000 00:00 0 
2af763c37000-2af763c49000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2af763c49000-2af763e48000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2af763e48000-2af763e49000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2af763e49000-2af763e4a000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2af763e4a000-2af763e4b000 rw-p 00000000 00:00 0 
2af763e4b000-2af763ea6000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2af763ea6000-2af7640a6000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2af7640a6000-2af7640a7000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2af7640a7000-2af7640ac000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2af7640ac000-2af7640b7000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2af7640b7000-2af7642b6000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2af7642b6000-2af7642b7000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2af7642b7000-2af7642b8000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2af7642b8000-2af7642bb000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2af7642bb000-2af7644bb000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2af7644bb000-2af7644bc000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2af7644bc000-2af7644bd000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2af7644bd000-2af7644c1000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2af7644c1000-2af7646c0000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2af7646c0000-2af7646c1000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2af7646c1000-2af7646c2000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2af7646c2000-2af764747000 rw-p 00000000 00:00 0 
2af764747000-2af764748000 ---p 00000000 00:00 0 
2af764748000-2af764a00000 rw-p 00000000 00:00 0 
2af764a00000-2af765000000 rw-p 00000000 00:00 0 
2af765000000-2af765200000 rw-p 00000000 00:00 0 
2af765200000-2af767800000 rw-p 00000000 00:00 0 
2af767800000-2af767a00000 rw-p 00000000 00:00 0 
2af767a00000-2af768e00000 rw-p 00000000 00:00 0 
2af768e00000-2af768fd0000 rw-p 00000000 00:00 0 
2af76c000000-2af76c021000 rw-p 00000000 00:00 0 
2af76c021000-2af770000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003200000 rw-p 00000000 00:00 0 
7ffe97348000-7ffe97376000 rw-p 00000000 00:00 0                          [stack]
7ffe973a2000-7ffe973a4000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
