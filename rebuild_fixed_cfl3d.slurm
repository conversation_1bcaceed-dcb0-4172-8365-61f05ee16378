#!/bin/bash
#SBATCH --job-name=rebuild_cfl3d_fixed
#SBATCH --partition=cpu
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=16G
#SBATCH --time=02:00:00
#SBATCH --output=rebuild_cfl3d_fixed_%j.out
#SBATCH --error=rebuild_cfl3d_fixed_%j.err

# 打印作业信息
echo "=========================================="
echo "SLURM Job ID: $SLURM_JOB_ID"
echo "SLURM Node List: $SLURM_JOB_NODELIST"
echo "Running on host: $(hostname)"
echo "Initial working directory: $(pwd)"
echo "=========================================="

# 加载必要的模块
echo "Loading modules..."
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

echo "当前加载的模块:"
module list

echo "=========================================="

# 进入CFL3D-SR的build目录
cd /home/<USER>/gpuuser255/lmc/CFL3D-SR/build || {
    echo "Error: Cannot change to CFL3D-SR build directory"
    exit 1
}

echo "Current working directory: $(pwd)"
echo "=========================================="

# 验证源文件修复
echo "验证源文件修复..."
grep -n "ifree = 0" ../source/cfl3d/dist/cfl3d.F
if [ $? -eq 0 ]; then
    echo "✅ 源文件修复已确认"
else
    echo "❌ 源文件修复未找到"
    exit 1
fi

echo "=========================================="

# 清理之前的编译文件
echo "Cleaning previous build files..."
make scruball -f makefile_intel

# 创建软链接
echo "Creating symbolic links..."
make linkall -f makefile_intel

# 编译公共库
echo "Building CFL3D libraries..."
make cfl3d_libs -f makefile_intel -j 8

# 编译MPI版本
echo "Building CFL3D MPI version with memory fix..."
make cfl3d_mpi -f makefile_intel -j 8

echo "=========================================="
echo "Build completed!"
echo "Checking if executable was created..."

if [ -f "cfl/mpi/cfl3d_mpi" ]; then
    echo "✅ SUCCESS: Fixed cfl3d_mpi executable created successfully!"
    echo "Location: $(pwd)/cfl/mpi/cfl3d_mpi"
    ls -la cfl/mpi/cfl3d_mpi
    
    # 备份旧版本
    if [ -f "cfl/mpi/cfl3d_mpi.backup" ]; then
        echo "备份文件已存在，跳过备份"
    else
        echo "备份原始版本..."
        cp cfl/mpi/cfl3d_mpi cfl/mpi/cfl3d_mpi.backup
    fi
    
    echo "✅ 修复版本已准备就绪"
else
    echo "❌ ERROR: cfl3d_mpi executable not found!"
    echo "Check the build log for errors."
    exit 1
fi

echo "=========================================="
echo "验证编译的可执行文件包含修复..."

# 检查编译时间
echo "可执行文件时间戳:"
stat cfl/mpi/cfl3d_mpi

echo "=========================================="
echo "Build job completed at $(date)"
echo "现在可以使用修复版本的CFL3D进行计算了！"
