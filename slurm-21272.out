Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21272
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21272
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 150.38 seconds.
  Return Code: 255
  STDOUT:
           0  of           2  is alive
           1  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 32606 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 32607 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x0000000001a402e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2ba111943299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc638cf]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x691027]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8bda95]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2ba1118e4555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f3e000 r-xp 00000000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113d000-0113f000 r--p 00b3d000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113f000-01197000 rw-p 00b3f000 00:29 495501364                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01197000-014ab000 rw-p 00000000 00:00 0 
0197b000-02028000 rw-p 00000000 00:00 0                                  [heap]
2ba10f18b000-2ba10f1ad000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ba10f1ad000-2ba10f1b7000 rw-p 00000000 00:00 0 
2ba10f1b7000-2ba10f1b8000 rw-s 00000000 00:2f 2595415092                 /dev/shm/Intel_MPI_OTyIoS (deleted)
2ba10f1b8000-2ba10f1b9000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1b9000-2ba10f1ba000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ba000-2ba10f1bb000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1bb000-2ba10f1bc000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1bc000-2ba10f1be000 rw-p 00000000 00:00 0 
2ba10f1be000-2ba10f1c2000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ba10f1c2000-2ba10f1d4000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ba10f1d4000-2ba10f1d7000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ba10f1d7000-2ba10f1d8000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ba10f1d8000-2ba10f1d9000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ba10f1d9000-2ba10f1e5000 rw-p 00000000 00:00 0 
2ba10f1e5000-2ba10f1e6000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1e6000-2ba10f1e7000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1e7000-2ba10f1e8000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1e8000-2ba10f1e9000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1e9000-2ba10f1ea000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ea000-2ba10f1eb000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1eb000-2ba10f1ec000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ec000-2ba10f1ed000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ed000-2ba10f1ee000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ee000-2ba10f1ef000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ef000-2ba10f1f0000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f0000-2ba10f1f1000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f1000-2ba10f1f2000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f2000-2ba10f1f3000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f3000-2ba10f1f4000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f4000-2ba10f1f5000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1f5000-2ba10f1f7000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ba10f1f7000-2ba10f1fb000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ba10f1fb000-2ba10f1fc000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ba10f1fc000-2ba10f1fd000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ba10f1fd000-2ba10f1fe000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ba10f1fe000-2ba10f1ff000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f1ff000-2ba10f200000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f200000-2ba10f201000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f201000-2ba10f202000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f202000-2ba10f203000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f203000-2ba10f204000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f204000-2ba10f205000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f205000-2ba10f206000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f206000-2ba10f207000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f207000-2ba10f208000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f208000-2ba10f209000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f209000-2ba10f20a000 rw-p 00000000 00:00 0 
2ba10f20a000-2ba10f20b000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2ba10f20b000-2ba10f20e000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ba10f20e000-2ba10f21d000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ba10f21d000-2ba10f224000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ba10f224000-2ba10f225000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ba10f225000-2ba10f226000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ba10f226000-2ba10f39e000 rw-p 00000000 00:00 0 
2ba10f3ac000-2ba10f3ad000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ba10f3ad000-2ba10f3ae000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ba10f3ae000-2ba10f3af000 rw-p 00000000 00:00 0 
2ba10f3af000-2ba10f537000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ba10f537000-2ba10f737000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ba10f737000-2ba10f73b000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ba10f73b000-2ba10f73f000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ba10f73f000-2ba10f763000 rw-p 00000000 00:00 0 
2ba10f763000-2ba1105ed000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ba1105ed000-2ba1107ed000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ba1107ed000-2ba1107fe000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ba1107fe000-2ba11080f000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ba11080f000-2ba110f98000 rw-p 00000000 00:00 0 
2ba110f98000-2ba110f9a000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ba110f9a000-2ba11119a000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ba11119a000-2ba11119b000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ba11119b000-2ba11119c000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ba11119c000-2ba1111a3000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ba1111a3000-2ba1113a2000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ba1113a2000-2ba1113a3000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ba1113a3000-2ba1113a4000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ba1113a4000-2ba1113bb000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ba1113bb000-2ba1115ba000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ba1115ba000-2ba1115bb000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ba1115bb000-2ba1115bc000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ba1115bc000-2ba1115c0000 rw-p 00000000 00:00 0 
2ba1115c0000-2ba1116c1000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ba1116c1000-2ba1118c0000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ba1118c0000-2ba1118c1000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ba1118c1000-2ba1118c2000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ba1118c2000-2ba1119b7000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119b7000-2ba1119b8000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119b8000-2ba1119ba000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119ba000-2ba1119bb000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119bb000-2ba1119c1000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119c1000-2ba1119c3000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba1119c3000-2ba111a85000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba111a85000-2ba111c85000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba111c85000-2ba111c89000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba111c89000-2ba111c8b000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ba111c8b000-2ba111c90000 rw-p 00000000 00:00 0 
2ba111c90000-2ba111c9a000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ba111c9a000-2ba111e9a000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ba111e9a000-2ba111e9b000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ba111e9b000-2ba111e9c000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ba111e9c000-2ba1b566e000 rw-s 00000000 00:2f 2595415093                 /dev/shm/Intel_MPI_UAERPq (deleted)
2ba1b566e000-2ba1b56b6000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ba1b56b6000-2ba1b58b5000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ba1b58b5000-2ba1b58b9000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ba1b58b9000-2ba1b5910000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ba1b5910000-2ba1b5b0f000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ba1b5b0f000-2ba1b5b13000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ba1b5b13000-2ba1b5b2a000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ba1b5b2a000-2ba1b5d29000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ba1b5d29000-2ba1b5d2a000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ba1b5d2a000-2ba1b5d2b000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ba1b5d2b000-2ba1b5d2c000 rw-p 00000000 00:00 0 
2ba1b5d2c000-2ba1b5d46000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ba1b5d46000-2ba1b5f45000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ba1b5f45000-2ba1b5f46000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ba1b5f46000-2ba1b5f47000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ba1b5f47000-2ba1b5f65000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ba1b5f65000-2ba1b6165000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ba1b6165000-2ba1b6167000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ba1b6167000-2ba1b6168000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ba1b6168000-2ba1b61cc000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ba1b61cc000-2ba1b63cb000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ba1b63cb000-2ba1b63ce000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ba1b63ce000-2ba1b63d3000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ba1b63d3000-2ba1b63d5000 rw-p 00000000 00:00 0 
2ba1b63d5000-2ba1b641d000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ba1b641d000-2ba1b661d000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ba1b661d000-2ba1b661e000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ba1b661e000-2ba1b661f000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ba1b661f000-2ba1b6621000 rw-p 00000000 00:00 0 
2ba1b6621000-2ba1b6625000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ba1b6625000-2ba1b6824000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ba1b6824000-2ba1b6825000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ba1b6825000-2ba1b6826000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ba1b6826000-2ba1b6831000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ba1b6831000-2ba1b6a30000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ba1b6a30000-2ba1b6a31000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ba1b6a31000-2ba1b6a32000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ba1b6a32000-2ba1b6a76000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ba1b6a76000-2ba1b6c76000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ba1b6c76000-2ba1b6c79000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ba1b6c79000-2ba1b6cd4000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ba1b6cd4000-2ba1b6ed3000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ba1b6ed3000-2ba1b6ed7000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ba1b6ed7000-2ba1b6f26000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ba1b6f26000-2ba1b7126000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ba1b7126000-2ba1b712a000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ba1b712a000-2ba1b717d000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ba1b717d000-2ba1b737d000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ba1b737d000-2ba1b7381000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ba1b7381000-2ba1b74b4000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ba1b74b4000-2ba1b76b3000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ba1b76b3000-2ba1b76bb000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ba1b76bb000-2ba1b76e1000 rw-p 00000000 00:00 0 
2ba1b76e1000-2ba1b7722000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ba1b7722000-2ba1b7922000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ba1b7922000-2ba1b7925000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ba1b7925000-2ba1b797e000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ba1b797e000-2ba1b7b7d000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ba1b7b7d000-2ba1b7b7e000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ba1b7b7e000-2ba1b7b81000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ba1b7b81000-2ba1b7ba7000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ba1b7ba7000-2ba1b7da7000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ba1b7da7000-2ba1b7da8000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ba1b7da8000-2ba1b7dac000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ba1b7dac000-2ba1b7eeb000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ba1b7eeb000-2ba1b80eb000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ba1b80eb000-2ba1b80fe000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ba1b80fe000-2ba1b8105000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ba1b8105000-2ba1b810d000 rw-p 00000000 00:00 0 
2ba1b810d000-2ba1b811f000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ba1b811f000-2ba1b831e000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ba1b831e000-2ba1b831f000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ba1b831f000-2ba1b8320000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ba1b8320000-2ba1b8321000 rw-p 00000000 00:00 0 
2ba1b8321000-2ba1b837c000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ba1b837c000-2ba1b857c000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ba1b857c000-2ba1b857d000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ba1b857d000-2ba1b8582000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ba1b8582000-2ba1b858d000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ba1b858d000-2ba1b878c000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ba1b878c000-2ba1b878d000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ba1b878d000-2ba1b878e000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ba1b878e000-2ba1b8791000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ba1b8791000-2ba1b8991000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ba1b8991000-2ba1b8992000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ba1b8992000-2ba1b8993000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ba1b8993000-2ba1b8997000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ba1b8997000-2ba1b8b96000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ba1b8b96000-2ba1b8b97000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ba1b8b97000-2ba1b8b98000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ba1b8b98000-2ba1b8c1d000 rw-p 00000000 00:00 0 
2ba1b8c1d000-2ba1b8c1e000 ---p 00000000 00:00 0 
2ba1b8c1e000-2ba1b9000000 rw-p 00000000 00:00 0 
2ba1b9000000-2ba1b9600000 rw-p 00000000 00:00 0 
2ba1b9600000-2ba1b9800000 rw-p 00000000 00:00 0 
2ba1b9800000-2ba1bbe00000 rw-p 00000000 00:00 0 
2ba1bbe00000-2ba1bc000000 rw-p 00000000 00:00 0 
2ba1bc000000-2ba1bd400000 rw-p 00000000 00:00 0 
2ba1bd400000-2ba1c3800000 rw-p 00000000 00:00 0 
2ba1c3800000-2ba1c4200000 rw-p 00000000 00:00 0 
2ba1c4200000-2ba1c43c2000 rw-p 00000000 00:00 0 
2ba1c8000000-2ba1c8021000 rw-p 00000000 00:00 0 
2ba1c8021000-2ba1cc000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003200000 rw-p 00000000 00:00 0 
7ffc3698f000-7ffc369b7000 rw-p 00000000 00:00 0                          [stack]
7ffc369e1000-7ffc369e3000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
