#!/bin/bash
#SBATCH --job-name=cfd_ppo       # 作业名称
#SBATCH --nodes=1                # 使用1个节点
#SBATCH --gres=gpu:1        # 每个节点分配4个GPU
#SBATCH --partition=gpu          # 指定gpu分区


# --- 设置执行目录 ---
# 重要：假设此脚本位于项目根目录下，并且你从项目根目录提交 sbatch run_cfd_ppo.sh
# 或者，取消下面的注释，并将其设置为项目的绝对路径
# PROJECT_DIR="/path/to/your/Agent-R1"
# cd $PROJECT_DIR || exit 1 # 如果更改目录失败则退出

echo "Current working directory: $(pwd)"
echo "Job submitted from: ${SLURM_SUBMIT_HOST}"
echo "Job ID: ${SLURM_JOB_ID}"
echo "GPUs allocated: ${CUDA_VISIBLE_DEVICES:-"Not Set (using all visible)"}" # SLURM通常会设置这个

# module unload intel/2022.1
module purge
module load cuda/12.4 
module load nccl/2.21.5-1-gcc11.3.0-cuda12.4
module load intel/oneapi/2022.1
module load miniforge/24.1.2
eval "$(conda shell.bash hook)"
conda activate verl3

echo "----------------------------------------------------"
echo "SLURM Job ID: $SLURM_JOB_ID"
echo "SLURM Node List: $SLURM_JOB_NODELIST"
echo "Running on host: $(hostname)"
echo "Initial working directory: $(pwd)"
echo "----------------------------------------------------"

# --- 用户可配置的参数 ---
# **** 请将此路径修改为您的 Agent-R1 (或类似项目) 项目根目录的绝对路径 ****
# 例如: PROJECT_ROOT_DIR="/home/<USER>/projects/Agent-R1-q3"
PROJECT_ROOT_DIR="/home/<USER>/gpuuser255/lmc/Agent-R1-q3" # 根据您的实际路径修改

# Python 脚本的名称
PYTHON_SCRIPT_NAME="run_cfl3d_in_test_dir.py"


echo "当前加载的模块:"
module list
echo "----------------------------------------------------"

# --- 导航到项目目录并执行 ---
echo "导航到项目根目录: ${PROJECT_ROOT_DIR}"
echo "Current working directory: $(pwd)"

if [ ! -f "${PYTHON_SCRIPT_NAME}" ]; then
    echo "[错误] Python 脚本 ${PYTHON_SCRIPT_NAME} 未在 ${PROJECT_ROOT_DIR} 中找到!"
    exit 1
fi

echo "开始运行 Python 脚本 (${PYTHON_SCRIPT_NAME})..."

# 执行 Python 脚本
python "${PYTHON_SCRIPT_NAME}"

EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo "----------------------------------------------------"
    echo "[成功] Python 脚本 (${PYTHON_SCRIPT_NAME}) 执行完成。"
    echo "----------------------------------------------------"
else
    echo "----------------------------------------------------"
    echo "[错误] Python 脚本 (${PYTHON_SCRIPT_NAME}) 执行失败，退出码: ${EXIT_CODE}。"
    echo "----------------------------------------------------"
fi

exit $EXIT_CODE 