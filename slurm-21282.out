Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21282
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21282
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 153.44 seconds.
  Return Code: 255
  STDOUT:
           1  of           2  is alive
           0  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 45382 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 45383 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x00000000026cf2e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2ab0edbe4299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc6380f]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x690f5e]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8bd9d5]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2ab0edb85555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f3e000 r-xp 00000000 00:29 495501785                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113d000-0113f000 r--p 00b3d000 00:29 495501785                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113f000-01197000 rw-p 00b3f000 00:29 495501785                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01197000-014ab000 rw-p 00000000 00:00 0 
0260a000-02cb7000 rw-p 00000000 00:00 0                                  [heap]
2ab0eb42c000-2ab0eb44e000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ab0eb44e000-2ab0eb458000 rw-p 00000000 00:00 0 
2ab0eb458000-2ab0eb459000 rw-s 00000000 00:2d 2595468470                 /dev/shm/Intel_MPI_hPaD2K (deleted)
2ab0eb459000-2ab0eb45a000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb45a000-2ab0eb45b000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb45b000-2ab0eb45c000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb45c000-2ab0eb45d000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb45d000-2ab0eb45f000 rw-p 00000000 00:00 0 
2ab0eb45f000-2ab0eb463000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ab0eb463000-2ab0eb475000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ab0eb475000-2ab0eb478000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ab0eb478000-2ab0eb479000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ab0eb479000-2ab0eb47a000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2ab0eb47a000-2ab0eb486000 rw-p 00000000 00:00 0 
2ab0eb486000-2ab0eb487000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb487000-2ab0eb488000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb488000-2ab0eb489000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb489000-2ab0eb48a000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48a000-2ab0eb48b000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48b000-2ab0eb48c000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48c000-2ab0eb48d000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48d000-2ab0eb48e000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48e000-2ab0eb48f000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb48f000-2ab0eb490000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb490000-2ab0eb491000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb491000-2ab0eb492000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb492000-2ab0eb493000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb493000-2ab0eb494000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb494000-2ab0eb495000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb495000-2ab0eb496000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb496000-2ab0eb498000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ab0eb498000-2ab0eb49c000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ab0eb49c000-2ab0eb49d000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ab0eb49d000-2ab0eb49e000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ab0eb49e000-2ab0eb49f000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2ab0eb49f000-2ab0eb4a0000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a0000-2ab0eb4a1000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a1000-2ab0eb4a2000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a2000-2ab0eb4a3000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a3000-2ab0eb4a4000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a4000-2ab0eb4a5000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a5000-2ab0eb4a6000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a6000-2ab0eb4a7000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a7000-2ab0eb4a8000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a8000-2ab0eb4a9000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4a9000-2ab0eb4aa000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4aa000-2ab0eb4ab000 rw-p 00000000 00:00 0 
2ab0eb4ab000-2ab0eb4ac000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2ab0eb4ac000-2ab0eb4af000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ab0eb4af000-2ab0eb4be000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ab0eb4be000-2ab0eb4c5000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ab0eb4c5000-2ab0eb4c6000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ab0eb4c6000-2ab0eb4c7000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2ab0eb4c7000-2ab0eb63f000 rw-p 00000000 00:00 0 
2ab0eb64d000-2ab0eb64e000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ab0eb64e000-2ab0eb64f000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2ab0eb64f000-2ab0eb650000 rw-p 00000000 00:00 0 
2ab0eb650000-2ab0eb7d8000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ab0eb7d8000-2ab0eb9d8000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ab0eb9d8000-2ab0eb9dc000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ab0eb9dc000-2ab0eb9e0000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2ab0eb9e0000-2ab0eba04000 rw-p 00000000 00:00 0 
2ab0eba04000-2ab0ec88e000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ab0ec88e000-2ab0eca8e000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ab0eca8e000-2ab0eca9f000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ab0eca9f000-2ab0ecab0000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2ab0ecab0000-2ab0ed239000 rw-p 00000000 00:00 0 
2ab0ed239000-2ab0ed23b000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ab0ed23b000-2ab0ed43b000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ab0ed43b000-2ab0ed43c000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ab0ed43c000-2ab0ed43d000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2ab0ed43d000-2ab0ed444000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ab0ed444000-2ab0ed643000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ab0ed643000-2ab0ed644000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ab0ed644000-2ab0ed645000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2ab0ed645000-2ab0ed65c000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ab0ed65c000-2ab0ed85b000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ab0ed85b000-2ab0ed85c000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ab0ed85c000-2ab0ed85d000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2ab0ed85d000-2ab0ed861000 rw-p 00000000 00:00 0 
2ab0ed861000-2ab0ed962000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ab0ed962000-2ab0edb61000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ab0edb61000-2ab0edb62000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ab0edb62000-2ab0edb63000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2ab0edb63000-2ab0edc58000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc58000-2ab0edc59000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc59000-2ab0edc5b000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc5b000-2ab0edc5c000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc5c000-2ab0edc62000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc62000-2ab0edc64000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edc64000-2ab0edd26000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edd26000-2ab0edf26000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edf26000-2ab0edf2a000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edf2a000-2ab0edf2c000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2ab0edf2c000-2ab0edf31000 rw-p 00000000 00:00 0 
2ab0edf31000-2ab0edf3b000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ab0edf3b000-2ab0ee13b000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ab0ee13b000-2ab0ee13c000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ab0ee13c000-2ab0ee13d000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2ab0ee13d000-2ab19190f000 rw-s 00000000 00:2d 2595468471                 /dev/shm/Intel_MPI_5tRjly (deleted)
2ab19190f000-2ab191957000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ab191957000-2ab191b56000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ab191b56000-2ab191b5a000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2ab191b5a000-2ab191bb1000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ab191bb1000-2ab191db0000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ab191db0000-2ab191db4000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2ab191db4000-2ab191dcb000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ab191dcb000-2ab191fca000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ab191fca000-2ab191fcb000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ab191fcb000-2ab191fcc000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2ab191fcc000-2ab191fcd000 rw-p 00000000 00:00 0 
2ab191fcd000-2ab191fe7000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ab191fe7000-2ab1921e6000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ab1921e6000-2ab1921e7000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ab1921e7000-2ab1921e8000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2ab1921e8000-2ab192206000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ab192206000-2ab192406000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ab192406000-2ab192408000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ab192408000-2ab192409000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2ab192409000-2ab19246d000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ab19246d000-2ab19266c000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ab19266c000-2ab19266f000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ab19266f000-2ab192674000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2ab192674000-2ab192676000 rw-p 00000000 00:00 0 
2ab192676000-2ab1926be000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ab1926be000-2ab1928be000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ab1928be000-2ab1928bf000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ab1928bf000-2ab1928c0000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2ab1928c0000-2ab1928c2000 rw-p 00000000 00:00 0 
2ab1928c2000-2ab1928c6000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ab1928c6000-2ab192ac5000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ab192ac5000-2ab192ac6000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ab192ac6000-2ab192ac7000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2ab192ac7000-2ab192ad2000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ab192ad2000-2ab192cd1000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ab192cd1000-2ab192cd2000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ab192cd2000-2ab192cd3000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2ab192cd3000-2ab192d17000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ab192d17000-2ab192f17000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ab192f17000-2ab192f1a000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2ab192f1a000-2ab192f75000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ab192f75000-2ab193174000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ab193174000-2ab193178000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2ab193178000-2ab1931c7000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ab1931c7000-2ab1933c7000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ab1933c7000-2ab1933cb000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2ab1933cb000-2ab19341e000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ab19341e000-2ab19361e000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ab19361e000-2ab193622000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2ab193622000-2ab193755000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ab193755000-2ab193954000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ab193954000-2ab19395c000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2ab19395c000-2ab193982000 rw-p 00000000 00:00 0 
2ab193982000-2ab1939c3000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ab1939c3000-2ab193bc3000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ab193bc3000-2ab193bc6000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2ab193bc6000-2ab193c1f000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ab193c1f000-2ab193e1e000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ab193e1e000-2ab193e1f000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ab193e1f000-2ab193e22000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2ab193e22000-2ab193e48000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ab193e48000-2ab194048000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ab194048000-2ab194049000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ab194049000-2ab19404d000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2ab19404d000-2ab19418c000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ab19418c000-2ab19438c000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ab19438c000-2ab19439f000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ab19439f000-2ab1943a6000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2ab1943a6000-2ab1943ae000 rw-p 00000000 00:00 0 
2ab1943ae000-2ab1943c0000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ab1943c0000-2ab1945bf000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ab1945bf000-2ab1945c0000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ab1945c0000-2ab1945c1000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2ab1945c1000-2ab1945c2000 rw-p 00000000 00:00 0 
2ab1945c2000-2ab19461d000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ab19461d000-2ab19481d000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ab19481d000-2ab19481e000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ab19481e000-2ab194823000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2ab194823000-2ab19482e000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ab19482e000-2ab194a2d000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ab194a2d000-2ab194a2e000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ab194a2e000-2ab194a2f000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2ab194a2f000-2ab194a32000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ab194a32000-2ab194c32000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ab194c32000-2ab194c33000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ab194c33000-2ab194c34000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2ab194c34000-2ab194c38000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ab194c38000-2ab194e37000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ab194e37000-2ab194e38000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ab194e38000-2ab194e39000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2ab194e39000-2ab194ebe000 rw-p 00000000 00:00 0 
2ab194ebe000-2ab194ebf000 ---p 00000000 00:00 0 
2ab194ebf000-2ab195200000 rw-p 00000000 00:00 0 
2ab195200000-2ab195800000 rw-p 00000000 00:00 0 
2ab195800000-2ab195a00000 rw-p 00000000 00:00 0 
2ab195a00000-2ab198000000 rw-p 00000000 00:00 0 
2ab198000000-2ab198200000 rw-p 00000000 00:00 0 
2ab198200000-2ab199600000 rw-p 00000000 00:00 0 
2ab199600000-2ab19fa62000 rw-p 00000000 00:00 0 
2ab1a0000000-2ab1a0021000 rw-p 00000000 00:00 0 
2ab1a0021000-2ab1a4000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000200000 rw-p 00000000 00:00 0 
7f2000000000-7f2003000000 rw-p 00000000 00:00 0 
7fffdad95000-7fffdadbd000 rw-p 00000000 00:00 0                          [stack]
7fffdade1000-7fffdade3000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
