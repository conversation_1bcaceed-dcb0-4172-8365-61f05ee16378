#     $Id$
#=============================================================================
#
#                    builds the executable for maggie
#
#=============================================================================

# ***************************** CREATE LINKS **********************************

link: lncode lnhead 

lncode:
	@ echo "         linking source code"
	ln -s $(MAGSRC)/*.F .

lnhead:
	@ echo "         linking header files"
	ln -s $(HEADER)/mag1.h .

# ***************************** SUFFIX RULES***********************************

#                    (to convert .F files to .f files)

.SUFFIXES: .F .TMP1

.F.f:
	$(CPP) $(CPPFLAG) $(CPPOPT) $*.F > $*.TMP1
	@grep -v '^[ ]*$$' < $*.TMP1 > $*.f
	@chmod 600 $*.f
	@rm $*.TMP1

# **************************** CREATE EXECUTABLE *****************************

SOURCE = maggie.f cputim.f wkstn.f

FSRC_SPEC =

OBJECT = $(SOURCE:.f=.o)

FOBJ_SPEC = $(FSRC_SPEC:.f=.o)

MAG_HEAD  = mag1.h

$(OBJECT): $(MAG_HEAD)
	$(FTN) $(FFLAG) -c $*.f

$(FOBJ_SPEC): $(MAG_HEAD)
	$(FTN) $(FFLAG_SPEC) -c $*.f

$(EXEC): $(SOURCE) $(OBJECT) $(FSRC_SPEC) $(FOBJ_SPEC)
	$(FTN) $(LFLAG) -o $(EXEC) $(OBJECT) $(FOBJ_SPEC) $(LLIBS)
	@ echo "                                                              "
	@ echo "=============================================================="
	@ echo "                                                              "
	@ echo "                  DONE:  $(EXEC) created                      "
	@ echo "                                                              "
	@ echo "          the sequential executable can be found in:          "
	@ echo "                                                              "
	@ echo "                      $(DIR)/$(EXEC)                          "
	@ echo "                                                              "
	@ echo "=============================================================="
	@ echo "                                                              "

# ****************************** CLEAN/SCRUB *********************************

# the @touch is used to (silently) create some temp files to prevent irksome
# warning messages are sometimes created if there are no *.whatever files and
# one tries to remove them

cleano:
	@touch temp.o
	-rm -f *.o

cleane:
	-rm -f $(EXEC)

cleanf:
	@touch temp.f
	-rm -f *.f

cleang:
	@touch temp.F
	-rm -f *.F

cleanh:
	@touch temp.h
	-rm -f *.h

scrub: cleano cleane cleanf cleang cleanh 
