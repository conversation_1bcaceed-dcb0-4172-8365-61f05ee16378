#!/bin/bash
# 交互式编译脚本 - 用于调试编译问题

echo "=========================================="
echo "CFL3D Interactive Build Script"
echo "=========================================="

# 检查当前目录
if [[ ! -f "makefile_intel" ]]; then
    echo "Error: Not in build directory or makefile_intel not found"
    echo "Please run this script from the build directory"
    exit 1
fi

# 加载模块
echo "Loading required modules..."
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

echo "Loaded modules:"
module list

echo "=========================================="

# 检查编译器
echo "Checking compilers..."
echo "Intel Fortran: $(which mpiifort)"
echo "Intel C: $(which mpiicc)"

echo "=========================================="

# 逐步编译
echo "Step 1: Cleaning previous build..."
make scruball -f makefile_intel

echo "Step 2: Creating symbolic links..."
make linkall -f makefile_intel

echo "Step 3: Building libraries..."
make cfl3d_libs -f makefile_intel -j 4

if [ $? -ne 0 ]; then
    echo "❌ Library build failed!"
    exit 1
fi

echo "Step 4: Building MPI executable..."
make cfl3d_mpi -f makefile_intel -j 4

if [ $? -ne 0 ]; then
    echo "❌ MPI executable build failed!"
    exit 1
fi

echo "=========================================="
echo "✅ Build completed successfully!"

if [ -f "cfl/mpi/cfl3d_mpi" ]; then
    echo "Executable location: $(pwd)/cfl/mpi/cfl3d_mpi"
    ls -la cfl/mpi/cfl3d_mpi
else
    echo "❌ Warning: Executable not found at expected location"
fi
