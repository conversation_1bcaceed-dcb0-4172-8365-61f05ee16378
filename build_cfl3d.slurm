#!/bin/bash
#SBATCH --job-name=build_cfl3d
#SBATCH --partition=cpu
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=16G
#SBATCH --time=02:00:00
#SBATCH --output=build_cfl3d_%j.out
#SBATCH --error=build_cfl3d_%j.err

# 打印作业信息
echo "=========================================="
echo "SLURM Job ID: $SLURM_JOB_ID"
echo "SLURM Node List: $SLURM_JOB_NODELIST"
echo "Running on host: $(hostname)"
echo "Initial working directory: $(pwd)"
echo "=========================================="

# 加载必要的模块
echo "Loading modules..."
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

echo "当前加载的模块:"
module list

echo "=========================================="

# 进入build目录
cd /home/<USER>/gpuuser255/lmc/Agent-R1-q3/build || {
    echo "Error: Cannot change to build directory"
    exit 1
}

echo "Current working directory: $(pwd)"
echo "=========================================="

# 清理之前的编译文件
echo "Cleaning previous build files..."
make scruball -f makefile_intel

# 创建软链接
echo "Creating symbolic links..."
make linkall -f makefile_intel

# 编译公共库
echo "Building CFL3D libraries..."
make cfl3d_libs -f makefile_intel -j 8

# 编译MPI版本
echo "Building CFL3D MPI version..."
make cfl3d_mpi -f makefile_intel -j 8

echo "=========================================="
echo "Build completed!"
echo "Checking if executable was created..."

if [ -f "cfl/mpi/cfl3d_mpi" ]; then
    echo "✅ SUCCESS: cfl3d_mpi executable created successfully!"
    echo "Location: $(pwd)/cfl/mpi/cfl3d_mpi"
    ls -la cfl/mpi/cfl3d_mpi
else
    echo "❌ ERROR: cfl3d_mpi executable not found!"
    echo "Check the build log for errors."
    exit 1
fi

echo "=========================================="
echo "Build job completed at $(date)"
