c  ---------------------------------------------------------------------------
c  CFL3D is a structured-grid, cell-centered, upwind-biased, Reynolds-averaged
c  Navier-Stokes (RANS) code. It can be run in parallel on multiple grid zones
c  with point-matched, patched, overset, or embedded connectivities. Both
c  multigrid and mesh sequencing are available in time-accurate or
c  steady-state modes.
c
c  Copyright 2001 United States Government as represented by the Administrator
c  of the National Aeronautics and Space Administration. All Rights Reserved.
c 
c  The CFL3D platform is licensed under the Apache License, Version 2.0 
c  (the "License"); you may not use this file except in compliance with the 
c  License. You may obtain a copy of the License at 
c  http://www.apache.org/licenses/LICENSE-2.0. 
c 
c  Unless required by applicable law or agreed to in writing, software 
c  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT 
c  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the 
c  License for the specific language governing permissions and limitations 
c  under the License.
c  ---------------------------------------------------------------------------
c
      subroutine dummy
c
c     $Id$
c
c***********************************************************************
c     Purpose: this is a placeholder for any development code; place
c     experimental code after this routine. For example, if a new
c     version of a subroutine is to be tested, it may be placed below.
c     When compiled, the version below will be used and the
c     original version ignored (at least this is true for *most* 
c     compilers). The exception to this is the main routine - any
c     changes to main must be made in main.F itself.
c
c     Note: this module (development.F) should always contain at least 
c     the following lines (without the comment characters):
c
c     subroutine dummy
c#  ifdef CMPLX
c     implicit complex(a-h,o-z)
c#  endif
c     return
c     end
c***********************************************************************
#   ifdef CMPLX
      implicit complex(a-h,o-z)
#   endif
c
      return
      end
