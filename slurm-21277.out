Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Job submitted from: bingxing-gpu-ln01
Job ID: 21277
GPUs allocated: 0
gcc-11.3.0 loaded successful
Loading 2022.1 version intel
----------------------------------------------------
SLURM Job ID: 21277
SLURM Node List: g0007
Running on host: g0007
Initial working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
----------------------------------------------------
当前加载的模块:
Currently Loaded Modulefiles:
  1) cuda/12.4                          4) intel/2022.1
  2) gcc/11.3.0-gcc-4.8.5               5) intel/oneapi/2022.1
  3) nccl/2.21.5-1-gcc11.3.0-cuda12.4   6) miniforge/24.1.2
----------------------------------------------------
导航到项目根目录: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
Current working directory: /home/<USER>/gpuuser255/lmc/Agent-R1-q3
开始运行 Python 脚本 (run_cfl3d_in_test_dir.py)...
--- 开始在 cfl3d_test 目录中直接运行 CFL3D ---
[Info] Project root (assumed): /home/<USER>/gpuuser255/lmc/Agent-R1-q3
[Info] Target execution directory (cfl3d_test): /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
[Info] Verified absolute path /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi (derived from relative path within cfl3d_test) exists.
[Exec] Executing command: mpirun -np 2 ../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi
        with CWD: /home/<USER>/gpuuser255/lmc/Agent-R1-q3/cfl3d_test
        with STDIN: 'y'
[Result] Subprocess finished in 156.25 seconds.
  Return Code: 255
  STDOUT:
           1  of           2  is alive
           0  of           2  is alive

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 0 PID 38790 RUNNING AT g0007
=   KILLED BY SIGNAL: 9 (Killed)
===================================================================================

===================================================================================
=   BAD TERMINATION OF ONE OF YOUR APPLICATION PROCESSES
=   RANK 1 PID 38791 RUNNING AT g0007
=   KILLED BY SIGNAL: 6 (Aborted)
===================================================================================

  STDERR:
*** Error in `../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi': free(): invalid next size (fast): 0x0000000002c4f2e0 ***
======= Backtrace: =========
/lib64/libc.so.6(+0x81299)[0x2b69fe32e299]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0xc6380f]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x690f5e]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x8bd9d5]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406912]
/lib64/libc.so.6(__libc_start_main+0xf5)[0x2b69fe2cf555]
../../CFL3D-SR/build/cfl/mpi/cfl3d_mpi[0x406829]
======= Memory map: ========
00400000-00f3e000 r-xp 00000000 00:29 495501655                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113d000-0113f000 r--p 00b3d000 00:29 495501655                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
0113f000-01197000 rw-p 00b3f000 00:29 495501655                          /home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi
01197000-014ab000 rw-p 00000000 00:00 0 
02b8a000-03237000 rw-p 00000000 00:00 0                                  [heap]
2b69fbb76000-2b69fbb98000 r-xp 00000000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b69fbb98000-2b69fbba2000 rw-p 00000000 00:00 0 
2b69fbba2000-2b69fbba3000 rw-s 00000000 00:2f 2595410762                 /dev/shm/Intel_MPI_M64GxJ (deleted)
2b69fbba3000-2b69fbba4000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbba4000-2b69fbba5000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbba5000-2b69fbba6000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbba6000-2b69fbba7000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbba7000-2b69fbba9000 rw-p 00000000 00:00 0 
2b69fbba9000-2b69fbbad000 r--p 00000000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b69fbbad000-2b69fbbbf000 r-xp 00004000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b69fbbbf000-2b69fbbc2000 r--p 00016000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b69fbbc2000-2b69fbbc3000 r--p 00019000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b69fbbc3000-2b69fbbc4000 rw-p 0001a000 00:29 331615819                  /home/<USER>/apps/miniforge/24.1.2/lib/libgcc_s.so.1
2b69fbbc4000-2b69fbbd0000 rw-p 00000000 00:00 0 
2b69fbbd0000-2b69fbbd1000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd1000-2b69fbbd2000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd2000-2b69fbbd3000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd3000-2b69fbbd4000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd4000-2b69fbbd5000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd5000-2b69fbbd6000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd6000-2b69fbbd7000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd7000-2b69fbbd8000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd8000-2b69fbbd9000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbd9000-2b69fbbda000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbda000-2b69fbbdb000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbdb000-2b69fbbdc000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbdc000-2b69fbbdd000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbdd000-2b69fbbde000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbde000-2b69fbbdf000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbdf000-2b69fbbe0000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbe0000-2b69fbbe2000 r--p 00000000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b69fbbe2000-2b69fbbe6000 r-xp 00002000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b69fbbe6000-2b69fbbe7000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b69fbbe7000-2b69fbbe8000 r--p 00006000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b69fbbe8000-2b69fbbe9000 rw-p 00007000 00:29 331903560                  /home/<USER>/apps/miniforge/24.1.2/lib/libuuid.so.1.3.0
2b69fbbe9000-2b69fbbea000 -w-s 00000000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbea000-2b69fbbeb000 -w-s 00001000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbeb000-2b69fbbec000 -w-s 00002000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbec000-2b69fbbed000 -w-s 00003000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbed000-2b69fbbee000 -w-s 00004000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbee000-2b69fbbef000 -w-s 00005000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbef000-2b69fbbf0000 -w-s 00006000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf0000-2b69fbbf1000 -w-s 00007000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf1000-2b69fbbf2000 r--s 00500000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf2000-2b69fbbf3000 r--s 00700000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf3000-2b69fbbf4000 rw-s 00800000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf4000-2b69fbbf5000 rw-p 00000000 00:00 0 
2b69fbbf5000-2b69fbbf6000 -w-s 00600000 00:05 12343                      /dev/infiniband/uverbs0
2b69fbbf6000-2b69fbbf9000 r--p 00000000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b69fbbf9000-2b69fbc08000 r-xp 00003000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b69fbc08000-2b69fbc0f000 r--p 00012000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b69fbc0f000-2b69fbc10000 r--p 00018000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b69fbc10000-2b69fbc11000 rw-p 00019000 00:29 331815889                  /home/<USER>/apps/miniforge/24.1.2/lib/libz.so.1.2.13
2b69fbc11000-2b69fbd89000 rw-p 00000000 00:00 0 
2b69fbd97000-2b69fbd98000 r--p 00021000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b69fbd98000-2b69fbd99000 rw-p 00022000 08:04 4719330                    /usr/lib64/ld-2.17.so
2b69fbd99000-2b69fbd9a000 rw-p 00000000 00:00 0 
2b69fbd9a000-2b69fbf22000 r-xp 00000000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b69fbf22000-2b69fc122000 ---p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b69fc122000-2b69fc126000 r--p 00188000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b69fc126000-2b69fc12a000 rw-p 0018c000 00:29 377223614                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/libmpifort.so.12.0.0
2b69fc12a000-2b69fc14e000 rw-p 00000000 00:00 0 
2b69fc14e000-2b69fcfd8000 r-xp 00000000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b69fcfd8000-2b69fd1d8000 ---p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b69fd1d8000-2b69fd1e9000 r--p 00e8a000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b69fd1e9000-2b69fd1fa000 rw-p 00e9b000 00:29 377234069                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/lib/release/libmpi.so.12.0.0
2b69fd1fa000-2b69fd983000 rw-p 00000000 00:00 0 
2b69fd983000-2b69fd985000 r-xp 00000000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b69fd985000-2b69fdb85000 ---p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b69fdb85000-2b69fdb86000 r--p 00002000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b69fdb86000-2b69fdb87000 rw-p 00003000 08:04 4719343                    /usr/lib64/libdl-2.17.so
2b69fdb87000-2b69fdb8e000 r-xp 00000000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b69fdb8e000-2b69fdd8d000 ---p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b69fdd8d000-2b69fdd8e000 r--p 00006000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b69fdd8e000-2b69fdd8f000 rw-p 00007000 08:04 4719367                    /usr/lib64/librt-2.17.so
2b69fdd8f000-2b69fdda6000 r-xp 00000000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b69fdda6000-2b69fdfa5000 ---p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b69fdfa5000-2b69fdfa6000 r--p 00016000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b69fdfa6000-2b69fdfa7000 rw-p 00017000 08:04 4719363                    /usr/lib64/libpthread-2.17.so
2b69fdfa7000-2b69fdfab000 rw-p 00000000 00:00 0 
2b69fdfab000-2b69fe0ac000 r-xp 00000000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b69fe0ac000-2b69fe2ab000 ---p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b69fe2ab000-2b69fe2ac000 r--p 00100000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b69fe2ac000-2b69fe2ad000 rw-p 00101000 08:04 4719345                    /usr/lib64/libm-2.17.so
2b69fe2ad000-2b69fe3a2000 r-xp 00000000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3a2000-2b69fe3a3000 r-xp 000f5000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3a3000-2b69fe3a5000 r-xp 000f6000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3a5000-2b69fe3a6000 r-xp 000f8000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3a6000-2b69fe3ac000 r-xp 000f9000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3ac000-2b69fe3ae000 r-xp 000ff000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe3ae000-2b69fe470000 r-xp 00101000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe470000-2b69fe670000 ---p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe670000-2b69fe674000 r--p 001c3000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe674000-2b69fe676000 rw-p 001c7000 08:04 4719337                    /usr/lib64/libc-2.17.so
2b69fe676000-2b69fe67b000 rw-p 00000000 00:00 0 
2b69fe67b000-2b69fe685000 r-xp 00000000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b69fe685000-2b69fe885000 ---p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b69fe885000-2b69fe886000 r--p 0000a000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b69fe886000-2b69fe887000 rw-p 0000b000 08:04 4724629                    /usr/lib64/libnuma.so.1.0.0
2b69fe887000-2b6aa2059000 rw-s 00000000 00:2f 2595410763                 /dev/shm/Intel_MPI_8noNxV (deleted)
2b6aa2059000-2b6aa20a1000 r-xp 00000000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b6aa20a1000-2b6aa22a0000 ---p 00048000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b6aa22a0000-2b6aa22a4000 rw-p 00047000 00:29 377219933                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/libfabric.so.1
2b6aa22a4000-2b6aa22fb000 r-xp 00000000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b6aa22fb000-2b6aa24fa000 ---p 00057000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b6aa24fa000-2b6aa24fe000 rw-p 00056000 00:29 376768322                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libverbs-1.1-fi.so
2b6aa24fe000-2b6aa2515000 r-xp 00000000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b6aa2515000-2b6aa2714000 ---p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b6aa2714000-2b6aa2715000 r--p 00016000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b6aa2715000-2b6aa2716000 rw-p 00017000 08:04 4732937                    /usr/lib64/librdmacm.so.1.2.28.0
2b6aa2716000-2b6aa2717000 rw-p 00000000 00:00 0 
2b6aa2717000-2b6aa2731000 r-xp 00000000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b6aa2731000-2b6aa2930000 ---p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b6aa2930000-2b6aa2931000 r--p 00019000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b6aa2931000-2b6aa2932000 rw-p 0001a000 08:04 4732369                    /usr/lib64/libibverbs.so.1.8.28.0
2b6aa2932000-2b6aa2950000 r-xp 00000000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b6aa2950000-2b6aa2b50000 ---p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b6aa2b50000-2b6aa2b52000 r--p 0001e000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b6aa2b52000-2b6aa2b53000 rw-p 00020000 08:04 4720191                    /usr/lib64/libnl-3.so.200.23.0
2b6aa2b53000-2b6aa2bb7000 r-xp 00000000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b6aa2bb7000-2b6aa2db6000 ---p 00064000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b6aa2db6000-2b6aa2db9000 r--p 00063000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b6aa2db9000-2b6aa2dbe000 rw-p 00066000 08:04 4720199                    /usr/lib64/libnl-route-3.so.200.23.0
2b6aa2dbe000-2b6aa2dc0000 rw-p 00000000 00:00 0 
2b6aa2dc0000-2b6aa2e08000 r-xp 00000000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b6aa2e08000-2b6aa3008000 ---p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b6aa3008000-2b6aa3009000 r--p 00048000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b6aa3009000-2b6aa300a000 rw-p 00049000 08:04 4732935                    /usr/lib64/libmlx5.so.1.12.28.0
2b6aa300a000-2b6aa300c000 rw-p 00000000 00:00 0 
2b6aa300c000-2b6aa3010000 r-xp 00000000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b6aa3010000-2b6aa320f000 ---p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b6aa320f000-2b6aa3210000 r--p 00003000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b6aa3210000-2b6aa3211000 rw-p 00004000 08:04 4854796                    /usr/lib64/libibverbs/librxe-rdmav25.so
2b6aa3211000-2b6aa321c000 r-xp 00000000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b6aa321c000-2b6aa341b000 ---p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b6aa341b000-2b6aa341c000 r--p 0000a000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b6aa341c000-2b6aa341d000 rw-p 0000b000 08:04 4732373                    /usr/lib64/libmlx4.so.1.0.28.0
2b6aa341d000-2b6aa3461000 r-xp 00000000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b6aa3461000-2b6aa3661000 ---p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b6aa3661000-2b6aa3664000 rw-p 00044000 00:29 376792396                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libtcp-fi.so
2b6aa3664000-2b6aa36bf000 r-xp 00000000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b6aa36bf000-2b6aa38be000 ---p 0005b000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b6aa38be000-2b6aa38c2000 rw-p 0005a000 00:29 376716277                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libsockets-fi.so
2b6aa38c2000-2b6aa3911000 r-xp 00000000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b6aa3911000-2b6aa3b11000 ---p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b6aa3b11000-2b6aa3b15000 rw-p 0004f000 00:29 377106847                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libshm-fi.so
2b6aa3b15000-2b6aa3b68000 r-xp 00000000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b6aa3b68000-2b6aa3d68000 ---p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b6aa3d68000-2b6aa3d6c000 rw-p 00053000 00:29 376768323                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/librxm-fi.so
2b6aa3d6c000-2b6aa3e9f000 r-xp 00000000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b6aa3e9f000-2b6aa409e000 ---p 00133000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b6aa409e000-2b6aa40a6000 rw-p 00132000 00:29 377198041                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libpsm3-fi.so
2b6aa40a6000-2b6aa40cc000 rw-p 00000000 00:00 0 
2b6aa40cc000-2b6aa410d000 r-xp 00000000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b6aa410d000-2b6aa430d000 ---p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b6aa430d000-2b6aa4310000 rw-p 00041000 00:29 376716278                  /home/<USER>/apps/oneapi/2022.1/mpi/2021.5.0/libfabric/lib/prov/libmlx-fi.so
2b6aa4310000-2b6aa4369000 r-xp 00000000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b6aa4369000-2b6aa4568000 ---p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b6aa4568000-2b6aa4569000 r--p 00058000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b6aa4569000-2b6aa456c000 rw-p 00059000 08:04 4733581                    /usr/lib64/libucp.so.0.0.0
2b6aa456c000-2b6aa4592000 r-xp 00000000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b6aa4592000-2b6aa4792000 ---p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b6aa4792000-2b6aa4793000 r--p 00026000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b6aa4793000-2b6aa4797000 rw-p 00027000 08:04 4733585                    /usr/lib64/libuct.so.0.0.0
2b6aa4797000-2b6aa48d6000 r-xp 00000000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b6aa48d6000-2b6aa4ad6000 ---p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b6aa4ad6000-2b6aa4ae9000 r--p 0013f000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b6aa4ae9000-2b6aa4af0000 rw-p 00152000 08:04 4733583                    /usr/lib64/libucs.so.0.0.0
2b6aa4af0000-2b6aa4af8000 rw-p 00000000 00:00 0 
2b6aa4af8000-2b6aa4b0a000 r-xp 00000000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b6aa4b0a000-2b6aa4d09000 ---p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b6aa4d09000-2b6aa4d0a000 r--p 00011000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b6aa4d0a000-2b6aa4d0b000 rw-p 00012000 08:04 4733579                    /usr/lib64/libucm.so.0.0.0
2b6aa4d0b000-2b6aa4d0c000 rw-p 00000000 00:00 0 
2b6aa4d0c000-2b6aa4d67000 r-xp 00000000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b6aa4d67000-2b6aa4f67000 ---p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b6aa4f67000-2b6aa4f68000 r--p 0005b000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b6aa4f68000-2b6aa4f6d000 rw-p 0005c000 08:04 4854887                    /usr/lib64/ucx/libuct_ib.so.0.0.0
2b6aa4f6d000-2b6aa4f78000 r-xp 00000000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b6aa4f78000-2b6aa5177000 ---p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b6aa5177000-2b6aa5178000 r--p 0000a000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b6aa5178000-2b6aa5179000 rw-p 0000b000 08:04 4854889                    /usr/lib64/ucx/libuct_rdmacm.so.0.0.0
2b6aa5179000-2b6aa517c000 r-xp 00000000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b6aa517c000-2b6aa537c000 ---p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b6aa537c000-2b6aa537d000 r--p 00003000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b6aa537d000-2b6aa537e000 rw-p 00004000 08:04 4854885                    /usr/lib64/ucx/libuct_cma.so.0.0.0
2b6aa537e000-2b6aa5382000 r-xp 00000000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b6aa5382000-2b6aa5581000 ---p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b6aa5581000-2b6aa5582000 r--p 00003000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b6aa5582000-2b6aa5583000 rw-p 00004000 08:04 4854891                    /usr/lib64/ucx/libuct_knem.so.0.0.0
2b6aa5583000-2b6aa5608000 rw-p 00000000 00:00 0 
2b6aa5608000-2b6aa5609000 ---p 00000000 00:00 0 
2b6aa5609000-2b6aa5a00000 rw-p 00000000 00:00 0 
2b6aa5a00000-2b6aa6000000 rw-p 00000000 00:00 0 
2b6aa6000000-2b6aa6200000 rw-p 00000000 00:00 0 
2b6aa6200000-2b6aa8800000 rw-p 00000000 00:00 0 
2b6aa8800000-2b6aa8a00000 rw-p 00000000 00:00 0 
2b6aa8a00000-2b6aa9e00000 rw-p 00000000 00:00 0 
2b6aa9e00000-2b6ab01ac000 rw-p 00000000 00:00 0 
2b6ab4000000-2b6ab4021000 rw-p 00000000 00:00 0 
2b6ab4021000-2b6ab8000000 ---p 00000000 00:00 0 
7f0000000000-7f0003000000 rw-p 00000000 00:00 0 
7f1000000000-7f1000400000 rw-p 00000000 00:00 0 
7f2000000000-7f2003000000 rw-p 00000000 00:00 0 
7ffffa6af000-7ffffa6d7000 rw-p 00000000 00:00 0                          [stack]
7ffffa715000-7ffffa717000 r-xp 00000000 00:00 0                          [vdso]
ffffffffff600000-ffffffffff601000 r-xp 00000000 00:00 0                  [vsyscall]

--- Direct CFL3D run in cfl3d_test_dir finished ---
----------------------------------------------------
[成功] Python 脚本 (run_cfl3d_in_test_dir.py) 执行完成。
----------------------------------------------------
