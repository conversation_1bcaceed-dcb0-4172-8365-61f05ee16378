#!/bin/bash
#SBATCH --job-name=cfl3d_fixed
#SBATCH --partition=cpu
#SBATCH --nodes=1
#SBATCH --ntasks=2
#SBATCH --cpus-per-task=1
#SBATCH --mem=8G
#SBATCH --time=01:00:00
#SBATCH --output=cfl3d_fixed_%j.out
#SBATCH --error=cfl3d_fixed_%j.err

# 打印作业信息
echo "=========================================="
echo "SLURM Job ID: $SLURM_JOB_ID"
echo "SLURM Node List: $SLURM_JOB_NODELIST"
echo "Running on host: $(hostname)"
echo "Initial working directory: $(pwd)"
echo "=========================================="

# 加载必要的模块
echo "Loading modules..."
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

echo "当前加载的模块:"
module list

echo "=========================================="

# 路径配置
PROJECT_ROOT_DIR="/home/<USER>/gpuuser255/lmc/Agent-R1-q3"
CFL3D_EXECUTABLE="/home/<USER>/gpuuser255/lmc/CFL3D-SR/build/cfl/mpi/cfl3d_mpi"
TEST_DIR="${PROJECT_ROOT_DIR}/cfl3d_test"

echo "项目根目录: ${PROJECT_ROOT_DIR}"
echo "CFL3D可执行文件: ${CFL3D_EXECUTABLE}"
echo "测试目录: ${TEST_DIR}"

# 验证路径
if [ ! -d "${PROJECT_ROOT_DIR}" ]; then
    echo "❌ 错误: 项目根目录不存在: ${PROJECT_ROOT_DIR}"
    exit 1
fi

if [ ! -f "${CFL3D_EXECUTABLE}" ]; then
    echo "❌ 错误: CFL3D可执行文件不存在: ${CFL3D_EXECUTABLE}"
    exit 1
fi

if [ ! -d "${TEST_DIR}" ]; then
    echo "❌ 错误: 测试目录不存在: ${TEST_DIR}"
    exit 1
fi

echo "✅ 所有路径验证通过"
echo "=========================================="

# 切换到测试目录
cd "${TEST_DIR}" || {
    echo "❌ 错误: 无法切换到测试目录: ${TEST_DIR}"
    exit 1
}

echo "当前工作目录: $(pwd)"
echo "目录内容:"
ls -la

echo "=========================================="

# 运行CFL3D
echo "开始运行CFL3D计算..."
echo "命令: mpirun -np 2 ${CFL3D_EXECUTABLE}"
echo "工作目录: ${TEST_DIR}"

# 记录开始时间
start_time=$(date +%s)

# 运行计算
echo "y" | mpirun -np 2 "${CFL3D_EXECUTABLE}"
exit_code=$?

# 记录结束时间
end_time=$(date +%s)
duration=$((end_time - start_time))

echo "=========================================="
echo "CFL3D计算完成"
echo "退出码: ${exit_code}"
echo "运行时间: ${duration} 秒"

if [ ${exit_code} -eq 0 ]; then
    echo "✅ 计算成功完成!"
else
    echo "❌ 计算失败，退出码: ${exit_code}"
fi

echo "=========================================="
echo "输出文件检查:"
ls -la *.out *.dat *.plt 2>/dev/null || echo "未找到输出文件"

echo "=========================================="
echo "作业完成时间: $(date)"
