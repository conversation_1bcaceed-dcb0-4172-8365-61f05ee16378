#!/bin/bash
# 快速重新编译脚本 - 应用完整的内存修复

echo "=========================================="
echo "快速重新编译CFL3D (应用完整内存修复)"
echo "=========================================="

# 进入CFL3D-SR的build目录
cd /home/<USER>/gpuuser255/lmc/CFL3D-SR/build || {
    echo "Error: Cannot change to CFL3D-SR build directory"
    exit 1
}

echo "当前目录: $(pwd)"

# 验证两个修复都已应用
echo "验证内存修复..."
echo "1. 检查 ifree = 0:"
grep -n "ifree = 0" ../source/cfl3d/dist/cfl3d.F

echo "2. 检查 deallocate(irdrea) 已注释:"
grep -A2 -B2 "deallocate(irdrea)" ../source/cfl3d/dist/cfl3d.F

echo "=========================================="

# 加载模块
module load gcc/11.3.0-gcc-4.8.5
module load intel/2022.1
module load intel/oneapi/2022.1
module load miniforge/24.1.2

# 只重新编译MPI版本 (库文件不需要重新编译)
echo "重新编译MPI可执行文件..."
cd cfl/mpi
make clean
make -f ../makefile cfl3d_mpi \
    FFLAG="-O2 -w -ip -fno-alias -xHost -traceback -I ../../cfl/libs  -r8 " \
    LFLAG="-z muldefs -xHost -traceback -fpe0" \
    FTN="mpiifort" \
    CPPOPT=" -DDIST_MPI -DDBLE_PRECSN -DP3D_SINGLE -DLINUX -DINTEL -DFASTIO -DGENERIC -DNOREDIRECT -DDBLE_PRECSN  "

if [ -f "cfl3d_mpi" ]; then
    echo "✅ 修复版本编译成功!"
    echo "文件信息:"
    ls -la cfl3d_mpi
    echo "编译时间: $(date)"
else
    echo "❌ 编译失败"
    exit 1
fi

echo "=========================================="
echo "完整内存修复已应用并编译完成!"
echo "现在可以测试新版本了"
